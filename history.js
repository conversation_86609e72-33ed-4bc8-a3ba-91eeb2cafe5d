const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// DOM 元素
const loadingState = document.getElementById('loadingState');
const emptyState = document.getElementById('emptyState');
const sessionsList = document.getElementById('sessionsList');

// 会话数据
let chatSessions = [];

// 等待国际化系统完全初始化
async function waitForI18nReady() {
  return new Promise((resolve) => {
    const checkI18n = () => {
      if (window.i18n && window.i18n.initialized && window.i18n.translations && Object.keys(window.i18n.translations).length > 0) {
        resolve();
      } else {
        setTimeout(checkI18n, 50);
      }
    };
    checkI18n();
  });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async () => {
  console.log('历史记录页面加载完成');

  // 等待国际化系统完全准备好
  await waitForI18nReady();

  // 确保国际化已初始化
  if (window.i18n) {
    console.log('国际化系统已准备好，当前语言:', window.i18n.getCurrentLanguage());

    // 更新页面标题
    const pageTitle = window.i18n.t('history.pageTitle');
    if (pageTitle) {
      document.title = pageTitle;
    }

    // 手动触发页面元素的国际化翻译
    window.i18n.updatePageContent();
    console.log('页面内容已更新为当前语言');
  }

  // 处理导航链接点击
  document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', (event) => {
      event.preventDefault();
      const href = link.getAttribute('href');
      if (href) {
        ipcRenderer.send('navigate', href);
      }
    });
  });

  // 加载会话列表
  await loadChatSessions();
});

// 加载聊天会话列表
async function loadChatSessions() {
  try {
    console.log('开始加载聊天会话列表...');

    // 显示加载状态
    showLoadingState();

    // 从主进程获取会话列表
    const sessions = await ipcRenderer.invoke('get-chat-sessions');

    if (sessions && sessions.length > 0) {
      chatSessions = sessions;
      console.log(`加载了 ${sessions.length} 个会话`);
      displaySessions(sessions);
    } else {
      console.log('没有找到聊天会话');
      showEmptyState();
    }
  } catch (error) {
    console.error('加载聊天会话失败:', error);
    showEmptyState();
  }
}

// 显示加载状态
function showLoadingState() {
  loadingState.style.display = 'block';
  emptyState.style.display = 'none';
  sessionsList.style.display = 'none';
}

// 显示空状态
function showEmptyState() {
  loadingState.style.display = 'none';
  emptyState.style.display = 'block';
  sessionsList.style.display = 'none';
}

// 显示会话列表
function displaySessions(sessions) {
  loadingState.style.display = 'none';
  emptyState.style.display = 'none';
  sessionsList.style.display = 'block';

  // 清空现有内容
  sessionsList.innerHTML = '';

  // 生成会话项
  sessions.forEach((session, index) => {
    const sessionItem = createSessionItem(session, index);
    sessionsList.appendChild(sessionItem);
  });
}

// 创建会话项元素
function createSessionItem(session, index) {
  const sessionDiv = document.createElement('div');
  sessionDiv.className = 'session-item';
  sessionDiv.onclick = () => openSession(session);

  // 会话图标
  const iconDiv = document.createElement('div');
  iconDiv.className = 'session-icon';
  iconDiv.textContent = '💬';

  // 会话内容
  const contentDiv = document.createElement('div');
  contentDiv.className = 'session-content';

  // 会话标题
  const titleDiv = document.createElement('div');
  titleDiv.className = 'session-title';
  titleDiv.textContent = session.title || `${window.i18n ? window.i18n.t('history.session') : '会话'} ${index + 1}`;

  // 会话预览
  const previewDiv = document.createElement('div');
  previewDiv.className = 'session-preview';
  previewDiv.textContent = session.preview || (window.i18n ? window.i18n.t('history.noPreview') : '暂无预览');

  // 会话元信息
  const metaDiv = document.createElement('div');
  metaDiv.className = 'session-meta';

  const timeSpan = document.createElement('span');
  timeSpan.className = 'session-time';
  timeSpan.textContent = formatTime(session.createdAt || session.timestamp);

  const countSpan = document.createElement('span');
  countSpan.className = 'session-count';
  countSpan.textContent = `${session.messageCount || 0} ${window.i18n ? window.i18n.t('history.messages') : '条消息'}`;

  metaDiv.appendChild(timeSpan);
  metaDiv.appendChild(countSpan);

  contentDiv.appendChild(titleDiv);
  contentDiv.appendChild(previewDiv);
  contentDiv.appendChild(metaDiv);

  // 操作按钮
  const actionsDiv = document.createElement('div');
  actionsDiv.className = 'session-actions';

  const deleteBtn = document.createElement('button');
  deleteBtn.className = 'delete-btn';
  deleteBtn.innerHTML = '🗑️';
  deleteBtn.title = window.i18n ? window.i18n.t('common.delete') : '删除';
  deleteBtn.onclick = (e) => {
    e.stopPropagation();
    deleteSession(session, index);
  };

  actionsDiv.appendChild(deleteBtn);

  sessionDiv.appendChild(iconDiv);
  sessionDiv.appendChild(contentDiv);
  sessionDiv.appendChild(actionsDiv);

  return sessionDiv;
}

// 格式化时间
function formatTime(timestamp) {
  if (!timestamp) return '';

  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now - date;
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    // 今天
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays === 1) {
    // 昨天
    return window.i18n ? window.i18n.t('history.yesterday') : '昨天';
  } else if (diffDays < 7) {
    // 一周内
    return `${diffDays}${window.i18n ? window.i18n.t('history.daysAgo') : '天前'}`;
  } else {
    // 超过一周
    return date.toLocaleDateString();
  }
}

// 打开会话
async function openSession(session) {
  try {
    console.log('打开会话:', session);

    // 通知主进程加载特定会话
    await ipcRenderer.invoke('load-chat-session', session.id);

    // 跳转回主页面
    ipcRenderer.send('navigate', 'index.html');
  } catch (error) {
    console.error('打开会话失败:', error);
    alert(window.i18n ? window.i18n.t('history.openError') : '打开会话失败');
  }
}

// 删除会话
async function deleteSession(session, index) {
  const confirmMessage = window.i18n ?
    window.i18n.t('history.deleteConfirm') :
    '确定要删除这个会话吗？';

  if (confirm(confirmMessage)) {
    try {
      console.log('删除会话:', session);

      // 通知主进程删除会话
      await ipcRenderer.invoke('delete-chat-session', session.id);

      // 从本地列表中移除
      chatSessions.splice(index, 1);

      // 重新显示列表
      if (chatSessions.length > 0) {
        displaySessions(chatSessions);
      } else {
        showEmptyState();
      }

      console.log('会话删除成功');
    } catch (error) {
      console.error('删除会话失败:', error);
      alert(window.i18n ? window.i18n.t('history.deleteError') : '删除会话失败');
    }
  }
}

// 返回按钮
function goBack() {
  ipcRenderer.send('navigate', 'index.html');
}


