const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const uiUtils = require('./uiUtils.js');
const AudioManager = require('./audioManager.js');
const SettingsManager = require('./settingsManager.js');
const Visualizer = require('./visualizer.js');

// DOM元素
const startBtn = document.getElementById('startBtn');
const stopBtn = document.getElementById('stopBtn');
const newChatBtn = document.getElementById('newChatBtn');
const status = document.getElementById('status');
const visualizerCanvasElement = document.getElementById('visualizer'); // Renamed for clarity
const silenceThresholdInput = document.getElementById('silenceThreshold');
const silenceDurationInput = document.getElementById('silenceDuration');
const transcriptionContainer = document.getElementById('transcription-container');
const responseContainer = document.getElementById('response-container');
const promptTypeSelect = document.getElementById('promptType');
const customPromptContainer = document.getElementById('customPromptContainer');
const customPromptTextarea = document.getElementById('customPrompt');
const audioDevicesSelect = document.getElementById('audioDevices');

// 清空聊天显示和内存记录的辅助函数
function clearChatDisplayAndMemory(showSystemMsg = false) {
  console.log('🧹 clearChatDisplayAndMemory被调用，showSystemMsg:', showSystemMsg);
  console.log('🧹 调用堆栈:', new Error().stack);

  transcriptionContainer.innerHTML = '';
  responseContainer.innerHTML = '';
  messageHistory.length = 0;

  console.log('🧹 已清空聊天显示和内存记录');

  if (showSystemMsg) {
    const message = window.i18n ? window.i18n.t('main.messages.newSessionStarted') : '新的录音会话开始，先前记录已清除。';
    displaySystemMessage(transcriptionContainer, message);
  }
}

// Global state variables (to be progressively reduced or managed by specific modules)
let isRecording = false; // This global one will reflect AudioManager's state for UI logic

// 存储历史记录
const messageHistory = [];
let currentUser = null;



// 添加一个通用的显示系统消息的函数
function displaySystemMessage(container, message, duration = 3000) {
  const systemMessage = document.createElement('div');
  systemMessage.className = 'system-message';
  systemMessage.textContent = message;

  container.appendChild(systemMessage);
  container.scrollTop = container.scrollHeight;

  // 自动移除消息
  setTimeout(() => {
    systemMessage.style.opacity = '0';
    setTimeout(() => {
      if (systemMessage.parentNode) {
        systemMessage.parentNode.removeChild(systemMessage);
      }
    }, 500);
  }, duration);

  return systemMessage;
}

// 添加弹窗提示函数
function showDialog(message, type = 'error', title = '提示') {
  // 移除已有的弹窗
  const existingDialog = document.querySelector('.custom-dialog-overlay');
  if (existingDialog) {
    existingDialog.remove();
  }

  // 创建弹窗覆盖层
  const overlay = document.createElement('div');
  overlay.className = 'custom-dialog-overlay';
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
  `;

  // 创建弹窗内容
  const dialog = document.createElement('div');
  dialog.className = 'custom-dialog';
  dialog.style.cssText = `
    background: white;
    border-radius: 8px;
    padding: 24px;
    max-width: 400px;
    min-width: 300px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: scale(0.9);
    transition: transform 0.3s ease;
  `;

  // 设置图标和颜色
  let icon = '❌';
  let iconColor = '#ff4757';
  if (type === 'warning') {
    icon = '⚠️';
    iconColor = '#ffa502';
  } else if (type === 'info') {
    icon = 'ℹ️';
    iconColor = '#3742fa';
  } else if (type === 'success') {
    icon = '✅';
    iconColor = '#2ed573';
  }

  dialog.innerHTML = `
    <div style="display: flex; align-items: center; margin-bottom: 16px;">
      <span style="font-size: 24px; margin-right: 12px;">${icon}</span>
      <h3 style="margin: 0; color: #2c3e50; font-size: 18px;">${title}</h3>
    </div>
    <p style="margin: 0 0 20px 0; color: #34495e; line-height: 1.5; font-size: 14px;">${message}</p>
    <div style="text-align: right;">
      <button class="dialog-btn" style="
        background: ${iconColor};
        color: white;
        border: none;
        padding: 8px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s ease;
      ">确定</button>
    </div>
  `;

  overlay.appendChild(dialog);
  document.body.appendChild(overlay);

  // 显示动画
  setTimeout(() => {
    overlay.style.opacity = '1';
    dialog.style.transform = 'scale(1)';
  }, 10);

  // 绑定关闭事件
  const closeDialog = () => {
    overlay.style.opacity = '0';
    dialog.style.transform = 'scale(0.9)';
    setTimeout(() => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
      }
    }, 300);
  };

  // 点击确定按钮关闭
  dialog.querySelector('.dialog-btn').addEventListener('click', closeDialog);

  // 点击覆盖层关闭
  overlay.addEventListener('click', (e) => {
    if (e.target === overlay) {
      closeDialog();
    }
  });

  // ESC键关闭
  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      closeDialog();
      document.removeEventListener('keydown', handleKeyDown);
    }
  };
  document.addEventListener('keydown', handleKeyDown);

  return overlay;
}

// 检测URL查询参数中是否有API地址设置
window.addEventListener('DOMContentLoaded', async () => {
  // 等待国际化初始化完成
  if (window.i18n) {
    await window.i18n.init();
    // 初始化完成后立即更新页面内容
    window.i18n.updatePageContent();
  }

  const urlParams = new URLSearchParams(window.location.search);
  const apiUrl = urlParams.get('api');
  if (apiUrl) {
    setBackendApiUrl(apiUrl);
  }

  // Initialize settings manager with DOM elements it needs to control/read initially
  SettingsManager.init(ipcRenderer, AudioManager, {
    silenceThresholdInput,
    silenceDurationInput,
    promptTypeSelect,
    customPromptContainer,
    customPromptTextarea
  });

  Visualizer.init(visualizerCanvasElement); // Initialize Visualizer

  // 检查用户登录状态
  checkLoginStatus();

  // 尝试恢复录音状态（默认不清除聊天历史）
  initializeAndRestoreState();

  // 监听语言切换事件
  window.addEventListener('languageChanged', () => {
    // 重新更新页面内容
    if (window.i18n) {
      window.i18n.updatePageContent();
    }

    // 更新动态生成的内容
    updateDynamicContent();
  });
});

// 更新动态内容
function updateDynamicContent() {
  if (!window.i18n) return;

  // 更新音频设备下拉菜单中的错误信息
  if (audioDevicesSelect && audioDevicesSelect.options.length === 1) {
    const option = audioDevicesSelect.options[0];
    if (option.value === '') {
      const errorMessage = window.i18n.t('settings.audio.microphoneError');
      option.textContent = errorMessage;
    }
  }

  // 更新状态文本
  if (status && !isRecording) {
    const readyText = window.i18n.t('main.status.ready');
    status.textContent = readyText;
  }

  // 更新清除聊天记录按钮文本（如果存在动态创建的按钮）
  const dynamicClearButton = document.querySelector('.control-buttons button[style*="background: #e74c3c"]');
  if (dynamicClearButton && !dynamicClearButton.hasAttribute('data-i18n')) {
    const clearText = window.i18n.t('main.clearChat');
    dynamicClearButton.textContent = clearText;
  }
}

// 设置后端API地址
function setBackendApiUrl(url) {
  if (!url) return;

  // 发送新的API地址到主进程
  ipcRenderer.send('set-api-url', url);

  // 添加信息提示
  displaySystemMessage(responseContainer, `API地址已更新: ${url}`);
}

// 加载音频设备列表
async function loadAudioDevices() {
  try {
    const initialSelectedDeviceId = SettingsManager.getSelectedAudioDeviceId();

    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    stream.getTracks().forEach(track => track.stop());

    const devices = await navigator.mediaDevices.enumerateDevices();
    const audioInputs = devices.filter(device => device.kind === 'audioinput');

    audioDevicesSelect.innerHTML = '';

    audioInputs.forEach(device => {
      const option = document.createElement('option');
      option.value = device.deviceId;
      option.text = device.label || `设备 ${audioDevicesSelect.options.length + 1}`;
      audioDevicesSelect.appendChild(option);
    });

    let finalSelectedId = '';
    if (initialSelectedDeviceId && audioInputs.some(d => d.deviceId === initialSelectedDeviceId)) {
      audioDevicesSelect.value = initialSelectedDeviceId;
      finalSelectedId = initialSelectedDeviceId;
    } else if (audioInputs.length > 0) {
      audioDevicesSelect.selectedIndex = 0;
      finalSelectedId = audioInputs[0].deviceId;
    }
    SettingsManager.setSelectedAudioDeviceId(finalSelectedId); // Update SettingsManager

    console.log('麦克风权限状态:', 'granted');
    console.log('loadAudioDevices 完成后，当前选择的音频设备ID (SettingsManager):', SettingsManager.getSelectedAudioDeviceId(), '下拉列表值为:', audioDevicesSelect.value);

  } catch (err) {
    console.error('获取麦克风设备失败:', err);
    audioDevicesSelect.innerHTML = '<option value="">无法访问麦克风</option>';
    SettingsManager.setSelectedAudioDeviceId(''); // Clear in SettingsManager
    console.log('麦克风权限状态:', 'denied');
  }
}

// 音频设备选择变更
audioDevicesSelect.addEventListener('change', () => {
  const newDeviceId = audioDevicesSelect.value;
  SettingsManager.setSelectedAudioDeviceId(newDeviceId);
  if (AudioManager.isRecording()) {
      console.warn("Device changed while recording. This might lead to issues. Stopping and restarting is recommended.");
      // AudioManager will pick up the new device ID on next configure/start call
      // Or, if AudioManager needs immediate reconfig on device change while running:
      // AudioManager.configure({ selectedAudioDeviceId: newDeviceId, /* other params */ });
  }
  // SettingsManager.saveCurrentRecordingConfigToMain(); // Called by setSelectedAudioDeviceId
});

// 开始录音
async function startRecording() {
  // 验证并获取可用的音频设备ID
  const currentSelectedDeviceId = validateAndGetAudioDeviceId();
  if (!currentSelectedDeviceId) {
    return; // 验证失败，已在validateAndGetAudioDeviceId中处理错误
  }

  console.log('startRecording: Attempting to start with deviceId:', currentSelectedDeviceId);

  AudioManager.configure({
    selectedAudioDeviceId: currentSelectedDeviceId,
    silenceThreshold: SettingsManager.getSilenceThreshold(),
    silenceDuration: SettingsManager.getSilenceDuration(),
    silenceCounterThreshold: SettingsManager.getSilenceCounterThreshold(),
    getCurrentPrompt: SettingsManager.getCurrentPromptText,
    onStatusUpdate: (msg, type) => {
      // 使用国际化文本
      let statusText = msg;
      if (window.i18n) {
        if (type === 'recording') {
          statusText = window.i18n.t('main.status.recording');
        } else if (type === 'processing') {
          statusText = window.i18n.t('main.status.processing');
        } else if (type === 'idle') {
          statusText = window.i18n.t('main.status.ready');
        }
      }
      status.textContent = statusText;
      status.className = (type === 'recording' || type === 'speaking' || type === 'processing') ? 'status recording' : 'status idle';
    },
    onRecordingStateChange: (recording) => {
      isRecording = recording;
      startBtn.disabled = recording;
      stopBtn.disabled = !recording;
       if (recording) Visualizer.setup(); // Call Visualizer.setup when recording starts
    },
    onDataReady: (audioData, prompt, format, timestamp) => {
      ipcRenderer.send('audio-data', { audioData, prompt, format, timestamp });
      status.textContent = 'Audio data sent, awaiting processing...';
    },
    onVisualizerData: Visualizer.draw // Pass Visualizer.draw method directly
  });

  const success = await AudioManager.start();

  if (success) {
    console.log('startRecording: AudioManager started successfully.');
    // 🔧 不清除聊天历史，保持当前显示的消息
    console.log('🔧 开始录音，保持当前聊天内容');
    ipcRenderer.send('user-started-recording');
    SettingsManager.saveCurrentRecordingConfigToMain();
  } else {
    console.error('startRecording: AudioManager failed to start.');
    ipcRenderer.send('user-stopped-recording');
    SettingsManager.saveCurrentRecordingConfigToMain();
  }
}

// 验证并获取可用的音频设备ID
function validateAndGetAudioDeviceId() {
  let currentSelectedDeviceId = SettingsManager.getSelectedAudioDeviceId();

  // 如果没有可用设备，返回null
  if (audioDevicesSelect.options.length === 0) {
    status.textContent = 'Error: No audio input devices available.';
    status.className = 'status idle';
    return null;
  }

  // 如果当前设备无效，使用第一个可用设备
  if (!currentSelectedDeviceId || !Array.from(audioDevicesSelect.options).some(opt => opt.value === currentSelectedDeviceId)) {
    currentSelectedDeviceId = audioDevicesSelect.options[0].value;
    SettingsManager.setSelectedAudioDeviceId(currentSelectedDeviceId);
    audioDevicesSelect.value = currentSelectedDeviceId;
  }

  return currentSelectedDeviceId;
}

// 停止录音
async function stopRecording() {
  console.log('stopRecording (global): User clicked stop or system initiated stop.');
  AudioManager.stop();
  ipcRenderer.send('user-stopped-recording');
  SettingsManager.saveCurrentRecordingConfigToMain(); // Save state (isRecording will be false via AudioManager)
  Visualizer.clear(); // Clear visualizer on stop

  // 立即更新UI状态
  isRecording = false;
  startBtn.disabled = false;
  stopBtn.disabled = true;
  status.textContent = '已停止录音';
  status.className = 'status idle';

  // 注意：不在这里保存会话，因为main.js已经在chat completion结束时自动保存了
  // 这样避免了重复保存的问题
  console.log('🎯 停止录音，会话保存由main.js在chat completion结束时处理');

  setTimeout(async () => {
    await loadAudioDevices(); // This reloads list and calls SettingsManager.setSelectedAudioDeviceId
    // 确保状态正确显示
    if (!AudioManager.isRecording()) {
      status.textContent = '未录音';
    }
  }, 500);
}



// 流式响应相关变量
let currentStreamMessageDiv = null;
let currentStreamMessageContent = '';

// 监听流式响应事件
ipcRenderer.on('audio-response-chunk', (_, data) => {

  if (data.status === 'chunk') {
    // 如果是新的对话开始（第一次收到chunk）
    if (!currentStreamMessageDiv) {
      // 创建新的消息容器
      const responseTimestamp = new Date();
      currentStreamMessageDiv = uiUtils.addMessage(responseContainer, '', false, responseTimestamp);
      currentStreamMessageContent = '';
      console.log('✓ 创建新的AI响应消息容器');
    }

    // 追加内容到当前消息
    if (data.content) {
      currentStreamMessageContent += data.content;
    }

    // 更新UI
    const textElement = currentStreamMessageDiv.querySelector('div:first-child');
    if (textElement) {
      try {
        // 使用marked渲染Markdown
        textElement.innerHTML = marked.parse(currentStreamMessageContent);

        // 添加代码高亮
        const codeBlocks = textElement.querySelectorAll('pre code');
        if (codeBlocks.length > 0) {
          // 添加highlight.js样式
          if (!document.querySelector('link[href*="highlight.js"]')) {
            const styleLink = document.createElement('link');
            styleLink.rel = 'stylesheet';
            styleLink.href = 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css';
            document.head.appendChild(styleLink);
          }
        }
      } catch (error) {
        console.error('Markdown解析错误:', error);
        textElement.textContent = currentStreamMessageContent;
      }
    }

    // 自动滚动到底部
    responseContainer.scrollTop = responseContainer.scrollHeight;
  }
  else if (data.status === 'done') {
    // 流式响应完成
    console.log('🏁 流式响应完成，最终内容长度:', currentStreamMessageContent.length);

    if (currentStreamMessageDiv && currentStreamMessageContent.trim()) {
      // 注意：不在这里保存到历史记录，因为main.js已经保存了
      // 只保存到本地messageHistory用于UI状态管理
      messageHistory.push({
        role: 'assistant',
        content: currentStreamMessageContent,
        timestamp: new Date()
      });
      console.log('✅ 已添加AI回复到本地历史记录（仅用于UI状态管理）');
    }

    // 重置流式响应变量
    currentStreamMessageDiv = null;
    currentStreamMessageContent = '';

    // 更新状态
    status.textContent = '监听中...';
  }
  else if (data.status === 'error') {
    console.error('🚨 流式响应错误:', data.error);

    // 如果是重复请求错误，不显示错误消息
    if (data.isDuplicate) {
      console.log('🔄 检测到重复请求，跳过处理');
    } else {
      // 使用弹窗显示错误消息
      showDialog(data.error, 'error', '处理错误');
    }

    // 重置流式响应变量
    currentStreamMessageDiv = null;
    currentStreamMessageContent = '';

    // 更新状态
    status.textContent = '监听中...';
  }
});

// 监听音频转录事件（主要的用户输入处理入口）
ipcRenderer.on('audio-transcription', (_, data) => {

  if (data.status === 'success' && data.transcription) {
    const transcriptionText = data.transcription.trim();
    const timestamp = new Date();

    uiUtils.addMessage(transcriptionContainer, transcriptionText, true, timestamp);
    console.log('✅ 已添加用户转录到界面:', transcriptionText);

    // 注意：不在这里保存到历史记录，因为main.js已经保存了
    // 只保存到本地messageHistory用于UI状态管理
    messageHistory.push({
      role: 'user',
      content: transcriptionText,
      timestamp: timestamp
    });
  } else {
    console.log('❌ 转录事件状态异常:', data);
  }
});

// 新的初始化和状态恢复总函数
async function initializeAndRestoreState(clearChat = false) {
  console.log('🔧 Unified init/restore process starting...');
  console.log('🔧 clearChat参数:', clearChat);
  let stateFromMain = null;
  try {
    stateFromMain = await ipcRenderer.invoke('get-recording-state');
    console.log('State from main process received:', stateFromMain);
  } catch (err) {
    console.error('Failed to get state from main process:', err);
  }

  // Load settings from main process and localStorage through SettingsManager
  // This also updates the DOM input elements for settings.
  SettingsManager.loadInitialStateFromMainAndStorage(stateFromMain);

  // Configure AudioManager with initial settings from SettingsManager
  // Callbacks are set here once, but settings values are from SettingsManager.
  AudioManager.configure({
    selectedAudioDeviceId: SettingsManager.getSelectedAudioDeviceId(),
    silenceThreshold: SettingsManager.getSilenceThreshold(),
    silenceDuration: SettingsManager.getSilenceDuration(),
    silenceCounterThreshold: SettingsManager.getSilenceCounterThreshold(),
    getCurrentPrompt: SettingsManager.getCurrentPromptText,
    onStatusUpdate: (msg, type) => {
      status.textContent = msg;
      status.className = (type === 'recording' || type === 'speaking' || type === 'processing') ? 'status recording' : 'status idle';
    },
    onRecordingStateChange: (recording) => {
      isRecording = recording;
      startBtn.disabled = recording;
      stopBtn.disabled = !recording;
       if (recording) Visualizer.setup(); // Call Visualizer.setup
       else Visualizer.clear(); // Clear visualizer if not recording
    },
    onDataReady: (audioData, prompt, format, timestamp) => {
      ipcRenderer.send('audio-data', { audioData, prompt, format, timestamp });
      status.textContent = 'Audio data sent, awaiting processing...';
    },
    onVisualizerData: Visualizer.draw
  });

  // Load audio devices list (this will also update SettingsManager with the final selected device ID)
  await loadAudioDevices();
  console.log('Audio devices loaded. Current selected in SettingsManager:', SettingsManager.getSelectedAudioDeviceId());
  // Dropdown UI (audioDevicesSelect.value) is set by loadAudioDevices

  // Application settings (prompts etc.) are already loaded by SettingsManager.loadInitialStateFromMainAndStorage
  // and UI elements like promptTypeSelect, customPromptTextarea are updated by it.
  console.log('Application settings (prompts) loaded by SettingsManager.');

  // Chat display logic - 只在需要时清除聊天历史
  console.log('🔧 检查是否需要清除聊天历史，clearChat:', clearChat);
  if (clearChat) {
    console.log('🧹 需要清除聊天历史，调用clearChatDisplayAndMemory');
    clearChatDisplayAndMemory();
    if (stateFromMain && stateFromMain.isRecording) {
      console.log('Restoring active recording session. UI cleared for this view of the session.');
    } else {
      console.log('Application started. Chat display cleared. History can be loaded manually.');
    }
  } else {
    console.log('✅ 跳过清除聊天历史，保留现有内容');
  }

  // Attempt to restore recording if stateFromMain indicates it
  if (stateFromMain && stateFromMain.isRecording) {
    console.log('Attempting to auto-restore recording session...');
    const deviceToUse = SettingsManager.getSelectedAudioDeviceId();
    if (deviceToUse && Array.from(audioDevicesSelect.options).some(opt => opt.value === deviceToUse)) {
      // AudioManager is already configured with latest settings from SettingsManager
      const success = await AudioManager.start();
      if (success) {
        console.log('Successfully restored recording session in renderer.');
      } else {
        console.error('Failed to auto-restore recording session in renderer.');
        ipcRenderer.send('user-stopped-recording');
        Visualizer.clear(); // Ensure visualizer is cleared if auto-start fails
      }
    } else {
      console.warn(`Cannot restore recording: device '${deviceToUse || ""}' not valid or no devices.`);
      isRecording = false;
      status.textContent = 'Stopped (device issue on restore)';
      status.className = 'status idle';
      startBtn.disabled = false;
      stopBtn.disabled = true;
      ipcRenderer.send('user-stopped-recording');
      Visualizer.clear();
    }
  } else {
    console.log('Main process state indicates not recording or no state. Ensuring idle state.');
    // 🔧 修复：只有在AudioManager实际在录音时才调用stop，避免不必要的警告
    if (AudioManager.isRecording()) {
      AudioManager.stop();
    }
    isRecording = false;
    status.textContent = 'Not recording';
    status.className = 'status idle';
    startBtn.disabled = false;
    stopBtn.disabled = true;
    Visualizer.clear();
  }

  // Final save of the fully initialized/restored state's settings
  SettingsManager.saveCurrentRecordingConfigToMain();
  console.log('Unified init/restore process finished. Final selected device:', SettingsManager.getSelectedAudioDeviceId(), 'Recording:', AudioManager.isRecording());
}

// 新建对话
async function newConversation() {
  console.log('💬 用户点击新建对话按钮');

  try {
    // 🔧 调用后端清空聊天历史并创建新会话
    const result = await ipcRenderer.invoke('new-conversation');

    if (result.success) {
      console.log('✅ 后端已清空聊天历史并创建新会话:', result.sessionId);

      // 清空前端页面显示的内容
      clearChatDisplayAndMemory();

      // 显示新对话开始的系统消息
      const message = window.i18n ? window.i18n.t('main.messages.newConversationStarted') : '新对话已开始，准备开始录音。';
      displaySystemMessage(transcriptionContainer, message);

      console.log('✅ 新对话已开始，前后端数据已清空');
    } else {
      console.error('❌ 新建对话失败:', result.message);
      // 即使后端失败，也清空前端显示
      clearChatDisplayAndMemory();
    }
  } catch (error) {
    console.error('❌ 新建对话出错:', error);
    // 出错时也清空前端显示
    clearChatDisplayAndMemory();
  }
}

// 处理页面退出
function handlePageUnload() {
  console.log('页面即将卸载，保存状态');

  // 只保存当前设置，不改变录音的启停状态
  // 确保其他设置（如当前选择的设备ID，静音阈值等）被保存。
  // 如果 stopRecording 被调用，它内部已经调用了 saveRecordingState。
  // 如果 isRecording 开始时为 false，我们也需要保存任何可能已更改的设置。
  SettingsManager.saveCurrentRecordingConfigToMain();
}

// 设置登录按钮事件处理
function setupLoginButton() {
  document.querySelectorAll('.login-button').forEach(link => {
    link.addEventListener('click', async (event) => {
      // 阻止默认行为
      event.preventDefault();

      // 保存状态并处理页面退出
      handlePageUnload();

      try {
        // 调用主进程打开网页端登录页面
        const result = await ipcRenderer.invoke('open-web-login');

        if (!result.success) {
          console.error('跳转到网页端登录失败:', result.error);
          // 如果网页端登录失败，回退到本地登录页面
          const href = link.getAttribute('href');
          if (href && href.includes('login.html')) {
            ipcRenderer.send('navigate', href + '?noAutoLogin=true');
          }
        }
      } catch (error) {
        console.error('跳转到网页端登录过程中发生错误:', error);
        // 出错时回退到本地登录页面
        const href = link.getAttribute('href');
        if (href) {
          ipcRenderer.send('navigate', href);
        }
      }
    });
  });
}

// 修改导航链接处理
document.addEventListener('DOMContentLoaded', () => {
  // 处理导航链接点击
  document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', (event) => {
      // 阻止默认行为
      event.preventDefault();

      // 保存状态并处理页面退出
      handlePageUnload();

      // 使用IPC导航
      const href = link.getAttribute('href');
      if (href) {
        ipcRenderer.send('navigate', href);
      }
    });
  });

  // 特殊处理"使用指南"链接
  const userGuideLink = document.getElementById('userGuideLink');
  const footerUserGuideLink = document.getElementById('footerUserGuideLink');

  // 定义一个通用的处理函数
  const handleUserGuideLinkClick = async (event) => {
    // 阻止默认行为
    event.preventDefault();

    try {
      // 从环境变量获取网站URL
      const websiteUrlResult = await ipcRenderer.invoke('get-env-var', 'WEBSITE_URL');
      if (!websiteUrlResult.success || !websiteUrlResult.value) {
        throw new Error('无法获取网站URL，请检查环境配置');
      }

      // 使用shell模块打开浏览器
      const { shell } = require('electron');
      await shell.openExternal(websiteUrlResult.value);
    } catch (error) {
      console.error('跳转到使用指南页面失败:', error);

      // 使用弹窗显示错误提示
      showDialog(`跳转到使用指南页面失败: ${error.message}`, 'error', '跳转失败');
    }
  };

  // 为导航栏中的使用指南链接添加点击事件
  if (userGuideLink) {
    userGuideLink.addEventListener('click', handleUserGuideLinkClick);
  }

  // 为页脚中的使用指南链接添加点击事件
  if (footerUserGuideLink) {
    footerUserGuideLink.addEventListener('click', handleUserGuideLinkClick);
  }

  // 特殊处理登录按钮的点击事件
  setupLoginButton();
});

// 按钮事件监听
startBtn.addEventListener('click', startRecording);
stopBtn.addEventListener('click', () => {
  console.log('渲染器：用户点击了停止按钮。');
  stopRecording();
});
newChatBtn.addEventListener('click', newConversation);

// 加载聊天历史记录
async function loadChatHistory(showMessages = true) {
  try {
    console.log('🔄 开始加载聊天历史...');

    // 先清空现有消息容器和本地历史
    transcriptionContainer.innerHTML = '';
    responseContainer.innerHTML = '';
    messageHistory.length = 0;
    console.log('📝 已清空现有消息容器和本地历史');

    const history = await ipcRenderer.invoke('get-chat-history');
    console.log('📥 从main进程获取的历史记录:', history);

    if (history && Array.isArray(history) && history.length > 0) {
      console.log(`📊 已加载 ${history.length} 条聊天记录`);

      // 显示历史消息
      for (const message of history) {
        console.log('📝 处理消息:', message.role, message.content.substring(0, 50) + '...');
        if (message.role === 'user') {
          uiUtils.addMessage(transcriptionContainer, message.content, true);
        } else if (message.role === 'assistant') {
          uiUtils.addMessage(responseContainer, message.content, false);
        }
        messageHistory.push(message);
      }

      console.log(`✅ 加载并显示了 ${history.length} 条历史消息`);
      console.log('📊 当前messageHistory长度:', messageHistory.length);
      console.log('📊 transcriptionContainer子元素数量:', transcriptionContainer.children.length);
      console.log('📊 responseContainer子元素数量:', responseContainer.children.length);

      // 只在需要时显示成功消息
      if (showMessages) {
        const successMessage = window.i18n ?
          window.i18n.t('main.messages.historyLoaded', { count: history.length }) :
          `已加载 ${history.length} 条历史记录`;
        displaySystemMessage(transcriptionContainer, successMessage);
      }
    } else {
      console.log('❌ 没有找到历史聊天记录');

      // 只在需要时显示无历史记录消息
      if (showMessages) {
        const noHistoryMessage = window.i18n ? window.i18n.t('main.messages.noHistory') : '暂无历史记录';
        displaySystemMessage(transcriptionContainer, noHistoryMessage);
      }
    }
  } catch (error) {
    console.error('💥 加载聊天历史失败:', error);

    // 只在需要时显示错误消息
    if (showMessages) {
      const errorMessage = window.i18n ? window.i18n.t('main.messages.historyLoadError') : '加载历史记录失败';
      displaySystemMessage(transcriptionContainer, errorMessage);
    }
  }
}

// 监听页面加载完成事件，用于页面导航后恢复状态
ipcRenderer.on('page-loaded', async () => {
  console.log('🔄 页面已加载完成，正在恢复状态...');

  // 重新初始化SettingsManager
  SettingsManager.init(ipcRenderer, AudioManager, {
    silenceThresholdInput,
    silenceDurationInput,
    promptTypeSelect,
    customPromptContainer,
    customPromptTextarea
  });

  // 先恢复状态，但不清除聊天历史
  console.log('🔧 开始恢复应用状态（不清除聊天历史）...');
  await initializeAndRestoreState(false); // 不清除聊天历史

  // 然后检查是否有聊天历史需要加载
  const history = await ipcRenderer.invoke('get-chat-history');
  if (history && Array.isArray(history) && history.length > 0) {
    console.log('📥 检测到聊天历史，自动加载...');
    await loadChatHistory(false); // 不显示系统消息
  }

  console.log('✅ 页面状态已恢复');
});

// 添加帮助信息
const helpInfo = document.createElement('div');
helpInfo.className = 'help-info';
helpInfo.innerHTML = `
  <p><strong>如何捕获系统音频:</strong></p>
  <ol>
    <li>确保已安装Blackhole或类似虚拟音频设备</li>
    <li>在系统声音设置中，将Blackhole设为输出设备</li>
    <li>或创建多输出设备（Blackhole + 扬声器）</li>
    <li>在下拉菜单中选择Blackhole作为输入源</li>
  </ol>
`;
document.querySelector('.settings-panel').appendChild(helpInfo);

// 监听设备变更事件
navigator.mediaDevices.addEventListener('devicechange', () => {
  console.log('检测到设备变更，重新加载音频设备列表');
  loadAudioDevices();
});



// 在页面卸载前保存录音状态
window.addEventListener('beforeunload', () => {
    console.log('页面即将卸载，保存当前录音配置。');
    SettingsManager.saveCurrentRecordingConfigToMain();
});

// 更新用户界面显示状态
function updateUserInterface(userInfo) {
  const userProfileContainer = document.getElementById('userProfileContainer');
  const loginButtonContainer = document.getElementById('loginButtonContainer');

  if (userInfo) {
    currentUser = userInfo;
    showUserProfile(userInfo);
    if (userProfileContainer && loginButtonContainer) {
      userProfileContainer.style.display = 'flex';
      loginButtonContainer.style.display = 'none';
    }
  } else {
    currentUser = null;
    hideUserProfile();
    if (userProfileContainer && loginButtonContainer) {
      userProfileContainer.style.display = 'none';
      loginButtonContainer.style.display = 'flex';
    }
  }
}

// 检查用户是否已登录
async function checkLoginStatus() {
  try {
    console.log('检查登录状态...');

    // 首先尝试从localStorage获取用户信息
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
    const storedUserInfo = localStorage.getItem('userInfo');

    if (isLoggedIn && storedUserInfo) {
      const userInfo = JSON.parse(storedUserInfo);
      console.log('从localStorage获取用户信息成功:', userInfo.email);
      updateUserInterface(userInfo);
      return true;
    }

    // 如果localStorage中没有用户信息，从服务器获取
    console.log('尝试从服务器获取用户信息...');
    const response = await ipcRenderer.invoke('get-user-info', { skipCache: true });

    if (response && response.success && response.data) {
      const userInfo = response.data;
      console.log('从服务器获取用户信息成功:', userInfo);

      // 更新localStorage
      localStorage.setItem('userInfo', JSON.stringify(userInfo));
      localStorage.setItem('isLoggedIn', 'true');

      updateUserInterface(userInfo);
      return true;
    } else {
      console.log('用户未登录或获取信息失败');
      // 清除localStorage中的登录信息
      localStorage.removeItem('userToken');
      localStorage.removeItem('userInfo');
      localStorage.removeItem('isLoggedIn');

      updateUserInterface(null);
      return false;
    }
  } catch (error) {
    console.error('检查登录状态时出错:', error);
    // 清除localStorage中的登录信息
    localStorage.removeItem('userToken');
    localStorage.removeItem('userInfo');
    localStorage.removeItem('isLoggedIn');

    updateUserInterface(null);
    return false;
  }
}

// 显示用户信息
function showUserProfile(user) {
  const userProfileContainer = document.getElementById('userProfileContainer');
  const loginButtonContainer = document.getElementById('loginButtonContainer');
  const userEmailElement = document.getElementById('userEmail');
  const userAvatarElement = document.getElementById('userAvatar');

  console.log('显示用户信息:', user);

  if (userProfileContainer && loginButtonContainer && userEmailElement && userAvatarElement) {
    // 判断user的数据结构并正确获取email
    let email = '';
    if (user) {
      if (user.email) {
        // 直接有email属性
        email = user.email;
      } else if (user.data && user.data.email) {
        // 嵌套在data属性中
        email = user.data.email;
      } else if (typeof user === 'string') {
        // 直接是字符串
        email = user;
      }
    }

    // 显示用户信息
    userEmailElement.textContent = email || '未知用户';

    // 设置头像（使用邮箱首字母）
    const initial = (user.email && user.email.length > 0) ? user.email[0].toUpperCase() : 'U';
    userAvatarElement.textContent = initial;

    // 显示用户信息区域，隐藏登录按钮
    userProfileContainer.style.display = 'block';
    loginButtonContainer.style.display = 'none';

    // 添加头像点击事件（显示/隐藏下拉菜单）
    userAvatarElement.onclick = function(event) {
      const dropdown = document.getElementById('userDropdown');
      if (dropdown) {
        dropdown.classList.toggle('active');
        event.stopPropagation(); // 阻止事件冒泡
      }
    };

    // 添加点击其他区域关闭下拉菜单
    document.addEventListener('click', function(event) {
      const dropdown = document.getElementById('userDropdown');
      if (dropdown && dropdown.classList.contains('active') && !userAvatarElement.contains(event.target)) {
        dropdown.classList.remove('active');
      }
    });

    // 添加历史记录按钮事件
    const historyButton = document.querySelector('.dropdown-item[data-i18n="user.history"]');
    if (historyButton) {
      historyButton.onclick = function() {
        // 跳转到历史记录页面
        ipcRenderer.send('navigate', 'history.html');
        // 关闭下拉菜单
        const dropdown = document.getElementById('userDropdown');
        if (dropdown) {
          dropdown.classList.remove('active');
        }
      };
    }

    // 添加注销按钮事件
    const logoutButton = document.getElementById('logoutButton');
    if (logoutButton) {
      logoutButton.onclick = function() {
        logout();
      };
    }
  }
}

// 隐藏用户信息，显示登录按钮
function hideUserProfile() {
  const userProfileContainer = document.getElementById('userProfileContainer');
  const loginButtonContainer = document.getElementById('loginButtonContainer');
  const userEmailElement = document.getElementById('userEmail');
  const userAvatarElement = document.getElementById('userAvatar');

  console.log('隐藏用户信息，显示登录按钮');

  if (userProfileContainer && loginButtonContainer) {
    userProfileContainer.style.display = 'none';
    loginButtonContainer.style.display = 'flex';

    // 清除用户显示的信息
    if (userEmailElement) userEmailElement.textContent = '';
    if (userAvatarElement) userAvatarElement.textContent = '';
  }

  // 清除本地用户状态
  currentUser = null;

  // 关闭可能打开的用户下拉菜单
  const dropdown = document.getElementById('userDropdown');
  if (dropdown && dropdown.classList.contains('active')) {
    dropdown.classList.remove('active');
  }

  console.log('用户信息已隐藏，本地状态已清除');
}

// 注销函数
async function logout() {
  try {
    // 发送注销请求并传递强制清除参数
    await ipcRenderer.invoke('logout-user', { forceClearState: true });

    // 清除localStorage中的用户信息
    try {
      localStorage.removeItem('userToken');
      localStorage.removeItem('userInfo');
      localStorage.removeItem('isLoggedIn');
      console.log('已从localStorage中清除用户信息');
    } catch (err) {
      console.error('清除localStorage中用户信息失败:', err);
    }

    // 清除本地用户状态
    hideUserProfile();
    currentUser = null;

    // 显示注销成功消息
    showDialog('您已成功注销', 'success', '注销成功');

    // 强制检查登录状态，确保UI更新
    setTimeout(() => {
      checkLoginStatus();
    }, 300);

  } catch (error) {
    console.error('注销失败:', error);
  }
}

// 监听登录成功事件
ipcRenderer.on('login-success', (_, user) => {
  // 保存用户信息到当前会话和localStorage
  currentUser = user;

  // 保存到localStorage
  try {
    localStorage.setItem('userInfo', JSON.stringify(user));
    localStorage.setItem('isLoggedIn', 'true');

    // 如果响应中包含token也保存
    if (user.token) {
      localStorage.setItem('userToken', user.token);
    }

    console.log('登录成功，用户信息已保存到localStorage');
  } catch (err) {
    console.error('保存用户信息到localStorage失败:', err);
  }

  // 更新UI显示
  showUserProfile(user);

  // 显示登录成功消息
  const systemMessage = document.createElement('div');
  systemMessage.className = 'system-message';
  systemMessage.textContent = '登录成功，欢迎回来！';
  responseContainer.appendChild(systemMessage);

  // 3秒后自动移除消息
  setTimeout(() => {
    systemMessage.style.opacity = '0';
    setTimeout(() => {
      if (systemMessage.parentNode) {
        systemMessage.parentNode.removeChild(systemMessage);
      }
    }, 500);
  }, 3000);
});

// 页面加载时检查登录状态
document.addEventListener('DOMContentLoaded', () => {
  console.log('页面完成加载，检查登录状态...');

  // 初始化用户界面
  initializeUserInterface();

  // 监听OAuth登录成功事件
  ipcRenderer.on('oauth-login-success', (_, data) => {

    // 更新当前用户信息
    currentUser = data.user;

    // 保存到localStorage
    try {
      localStorage.setItem('userToken', data.token || '');
      localStorage.setItem('userInfo', JSON.stringify(data.user));
      localStorage.setItem('isLoggedIn', 'true');
      console.log('OAuth用户信息已保存到localStorage');
    } catch (err) {
      console.error('保存OAuth用户信息到localStorage失败:', err);
    }

    // 显示成功消息
    displaySystemMessage(responseContainer, `网页端登录成功，欢迎 ${data.user.email || '用户'}`);

    // 更新UI显示登录状态
    showUserProfile(data.user);
  });

  // 监听OAuth登录失败事件
  ipcRenderer.on('oauth-login-error', (_, data) => {

    // 使用弹窗显示错误消息
    showDialog(`网页端登录失败: ${data.error || '未知错误'}`, 'error', '登录失败');
  });

  setTimeout(() => {
    checkLoginStatus();
  }, 500); // 延迟半秒确保所有DOM元素已渲染
});

// 初始化用户界面
function initializeUserInterface() {
  console.log('初始化用户界面...');

  // 尝试从localStorage快速恢复用户状态
  try {
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
    const storedUserInfo = localStorage.getItem('userInfo');

    if (isLoggedIn && storedUserInfo) {
      const userInfo = JSON.parse(storedUserInfo);
      updateUserInterface(userInfo);
    } else {
      updateUserInterface(null);
    }
  } catch (err) {
    console.warn('初始化用户界面时获取localStorage失败:', err);
    updateUserInterface(null);
  }
}