const marked = require('marked'); // 引入marked库
const hljs = require('highlight.js'); // 引入highlight.js库

// 配置marked选项，添加代码高亮
marked.setOptions({
  gfm: true, // 启用GitHub风格Markdown
  breaks: true, // 允许回车换行
  silent: true, // 忽略错误
  highlight: function(code, lang) {
    // 使用highlight.js进行代码高亮
    const language = hljs.getLanguage(lang) ? lang : 'plaintext';
    return hljs.highlight(code, { language }).value;
  }
});

const uiUtils = {
  formatTime: function(date) {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  },

  _ensureHighlightStylesAdded: function() {
    // 确保 highlight.js CSS只添加一次
    if (!document.head.querySelector('link[href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css"]')) {
      const styleLink = document.createElement('link');
      styleLink.rel = 'stylesheet';
      styleLink.href = 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css';
      document.head.appendChild(styleLink);
    }
    // 确保自定义代码块样式只添加一次
    if (!document.getElementById('custom-code-block-styles')) {
      const styleElement = document.createElement('style');
      styleElement.id = 'custom-code-block-styles';
      styleElement.textContent = `
        pre {
          background-color: #f6f8fa;
          border-radius: 6px;
          padding: 16px;
          overflow: auto;
        }
        .hljs {
          background: transparent;
          padding: 0;
        }
      `;
      document.head.appendChild(styleElement);
    }
  },

  addMessage: function(container, text, isUser = false, timestamp = new Date()) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
    
    const messageText = document.createElement('div');
    
    if (isUser) {
      messageText.textContent = text;
    } else {
      try {
        messageText.innerHTML = marked.parse(text);
        
        const codeBlocks = messageText.querySelectorAll('pre code');
        if (codeBlocks.length > 0) {
          this._ensureHighlightStylesAdded();
        }
      } catch (error) {
        console.error('Markdown解析错误:', error);
        messageText.textContent = text;
      }
    }
    
    messageDiv.appendChild(messageText);
    
    const timeDiv = document.createElement('div');
    timeDiv.className = 'timestamp';
    timeDiv.textContent = this.formatTime(timestamp);
    messageDiv.appendChild(timeDiv);
    
    container.appendChild(messageDiv);
    container.scrollTop = container.scrollHeight;
    
    return messageDiv;
  }
};

module.exports = uiUtils; 