<!DOCTYPE html>
<html data-i18n-title="login.title">
<head>
  <meta charset="UTF-8">
  <title>MeetingGPT - Login</title>
  <style>
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f7fa;
      color: #2c3e50;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
    }

    .container {
      display: flex;
      width: 800px;
      height: 500px;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .left-panel {
      width: 40%;
      background-color: #1a73e8;
      color: white;
      padding: 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .right-panel {
      width: 60%;
      background-color: white;
      padding: 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    h1 {
      margin: 0 0 20px 0;
      font-size: 2em;
      font-weight: 500;
    }

    h2 {
      margin: 0 0 30px 0;
      font-size: 1.8em;
      font-weight: 500;
    }

    p {
      line-height: 1.6;
      margin-bottom: 30px;
    }

    .feature {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }

    .feature-icon {
      width: 24px;
      height: 24px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 10px;
    }

    button {
      background-color: #1a73e8;
      color: white;
      border: none;
      padding: 12px 30px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s;
      margin-top: 20px;
    }

    button:hover {
      background-color: #0d64d6;
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
    }

    .message-container {
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      padding: 10px 20px;
      border-radius: 4px;
      z-index: 9999;
    }

    .message-container.error {
      background-color: #ffebee;
      color: #e53935;
      border: 1px solid #e53935;
    }

    .message-container.info {
      background-color: #e3f2fd;
      color: #1976d2;
      border: 1px solid #1976d2;
    }

    .message-container.success {
      background-color: #e8f5e9;
      color: #43a047;
      border: 1px solid #43a047;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="left-panel">
      <h1 data-i18n="app.name">MeetingGPT</h1>
      <p>Smart meeting assistant for more efficient and intelligent meetings</p>

      <div class="feature">
        <div class="feature-icon">✓</div>
        <div data-i18n="login.features.realtime">Real-time voice recognition and processing</div>
      </div>
      <div class="feature">
        <div class="feature-icon">✓</div>
        <div data-i18n="login.features.ai">Multi-scenario AI assistant support</div>
      </div>
      <div class="feature">
        <div class="feature-icon">✓</div>
        <div data-i18n="login.features.summary">Automatic meeting content organization and summary</div>
      </div>
      <div class="feature">
        <div class="feature-icon">✓</div>
        <div data-i18n="login.features.sync">Multi-device support and data synchronization</div>
      </div>
    </div>

    <div class="right-panel">
      <h2 data-i18n="login.welcome">Welcome to MeetingGPT</h2>
      <p data-i18n="login.description">Click the button below to login via web</p>
      <button id="login-button" data-i18n="login.goToLogin">Go to Login</button>
    </div>
  </div>

  <script src="i18n.js"></script>
  <script src="languageSwitcher.js"></script>
  <script>
    const { ipcRenderer } = require('electron');
    const loginButton = document.getElementById('login-button');

    // 登录功能 - 使用网页端登录
    loginButton.addEventListener('click', async () => {
      try {
        // 显示正在跳转的消息
        showMessage('正在跳转到网页端登录...', 'info');

        // 调用主进程打开网页端登录页面
        const result = await ipcRenderer.invoke('open-web-login');

        if (!result.success) {
          showMessage('跳转到网页端登录失败: ' + (result.error || '未知错误'));
        }
      } catch (error) {
        showMessage('跳转到网页端登录过程中发生错误');
        console.error('跳转到网页端登录错误:', error);
      }
    });

    // 显示消息提示
    function showMessage(message, type = 'error') {
      // 移除已有的消息
      const existingMessage = document.querySelector('.message-container');
      if (existingMessage) {
        existingMessage.remove();
      }

      // 创建新消息元素
      const messageContainer = document.createElement('div');
      messageContainer.className = `message-container ${type}`;
      messageContainer.textContent = message;

      document.body.appendChild(messageContainer);

      // 3秒后自动移除
      setTimeout(() => {
        messageContainer.style.opacity = '0';
        messageContainer.style.transition = 'opacity 0.5s';
        setTimeout(() => {
          if (messageContainer.parentNode) {
            messageContainer.parentNode.removeChild(messageContainer);
          }
        }, 500);
      }, 3000);
    }

    // 检查用户是否已登录，如果已登录则直接跳转到主页
    async function checkLoginStatus() {
      try {
        // 在登录页面明确指明不要自动登录，即使有存储的凭据
        const user = await ipcRenderer.invoke('get-user-info', {
          skipCache: true,
          noAutoLogin: true,
          isLoginPage: true
        });

        if (user && user.success && user.isLoggedIn && user.userInfo) {
          console.log('检测到用户已登录:', user.userInfo.email);

          // 添加一个确认对话框，询问用户是否跳转
          if (confirm('您已经登录，是否返回主页面？')) {
            // 用户确认后跳转到主页
            ipcRenderer.send('navigate', 'index.html');
          }
        } else {
          console.log('用户未登录或已选择留在登录页面');
        }
      } catch (error) {
        console.error('检查登录状态出错:', error);
      }
    }

    // 监听登录页面加载事件
    ipcRenderer.on('login-page-loaded', (event, data) => {
      console.log('收到login-page-loaded事件，参数:', data);
      // 只有在没有noAutoLogin标志时才检查登录状态
      if (!data || !data.noAutoLogin) {
        checkLoginStatus();
      } else {
        console.log('已设置noAutoLogin，跳过自动登录检查');
      }
    });
  </script>
</body>
</html>