let visualizerCanvas = null;
let visualizerCtx = null;
let canvasWidth = 0;
let canvasHeight = 0;

const Visualizer = {
  init: (canvasElement) => {
    if (!canvasElement) {
      console.error('Visualizer: Canvas element not provided for initialization.');
      return;
    }
    visualizerCanvas = canvasElement;
    visualizerCtx = visualizerCanvas.getContext('2d');
    Visualizer.setup(); // Initial setup
    window.addEventListener('resize', Visualizer.setup); // Re-setup on resize
  },

  setup: () => {
    if (!visualizerCtx || !visualizerCanvas) {
        // console.warn('Visualizer: Context or Canvas not initialized, skipping setup.');
        return;
    }
    canvasWidth = visualizerCanvas.width = visualizerCanvas.offsetWidth;
    canvasHeight = visualizerCanvas.height = visualizerCanvas.offsetHeight;
    console.log('Visualizer: Setup complete.', { canvasWidth, canvasHeight });
  },

  draw: (dataArray) => {
    if (!visualizerCtx || !visualizerCanvas) {
        // console.warn('Visualizer: Context or Canvas not initialized, skipping draw.');
        return;
    }
    if (!dataArray || dataArray.length === 0) {
        // console.warn('Visualizer: No data to draw.');
        visualizerCtx.clearRect(0, 0, canvasWidth, canvasHeight); // Clear if no data
        return;
    }

    visualizerCtx.clearRect(0, 0, canvasWidth, canvasHeight);
    
    const barWidth = (canvasWidth / dataArray.length) * 2.5;
    let barHeight;
    let x = 0;

    visualizerCtx.fillStyle = '#3498db';
    
    for (let i = 0; i < dataArray.length; i++) {
      barHeight = dataArray[i] / 255 * canvasHeight;
      visualizerCtx.fillRect(x, canvasHeight - barHeight, barWidth, barHeight);
      x += barWidth + 1;
    }
  },

  clear: () => {
    if (!visualizerCtx || !visualizerCanvas) {
        // console.warn('Visualizer: Context or Canvas not initialized, skipping clear.');
        return;
    }
    visualizerCtx.clearRect(0, 0, canvasWidth, canvasHeight);
    console.log('Visualizer: Cleared.');
  }
};

module.exports = Visualizer; 