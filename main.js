const { app, BrowserWindow, ipc<PERSON>ain, Menu, dialog } = require('electron');
const path = require('path');
const url = require('url');
const fs = require('fs');
const dotenv = require('dotenv');
const apiClient = require('./api'); // 导入API客户端模块

// 加载环境变量
dotenv.config();
// 禁用Electron安全警告
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';

// 创建配置存储 (将在初始化后异步创建)
let store = null;

// 保持对窗口对象的全局引用，避免JavaScript对象被垃圾回收时窗口关闭
let mainWindow;

// 添加聊天历史记录存储
let chatHistory = [];

// 初始化录音状态，后续会从 store 加载
let recordingState = {
  isRecording: false,
  audioDeviceId: '',
  silenceThreshold: 0.05,
  silenceDuration: 1500,
  silence_counter_threshold: 3 // 确保此属性存在
};

// 设置应用全局状态
let appState = {
  isLoggedIn: false,
  token: null,
  userInfo: null,
  tempNoAutoLogin: false,
  subscription: null // 添加订阅信息
};

// 从环境变量中读取API基础URL
apiClient.setBaseUrl(process.env.API_BASE_URL || 'http://localhost:8082');

// 保存应用状态到存储
function saveAppState() {
  if (store) {
    if (appState.token) {
      store.set('user.token', appState.token);
    }
    if (appState.userInfo) {
      store.set('user.info', appState.userInfo);
    }
    console.log('应用状态已保存到存储');
  } else {
    console.warn('存储尚未初始化，应用状态仅在内存中更新');
  }
}

// 异步初始化 store 和加载配置
async function initStore() {
  try {
    // 使用动态导入
    const { default: Store } = await import('electron-store');
    store = new Store();

    // 从 store 加载录音状态
    const savedState = store.get('recordingState');
    if (savedState) {
      recordingState = savedState;
      console.log('从存储加载的录音状态:', recordingState);
    }

    // 从 store 加载聊天历史
    const savedHistory = store.get('chatHistory');
    if (savedHistory && Array.isArray(savedHistory)) {
      chatHistory = savedHistory;
      // console.log('从存储加载的聊天历史, 条数:', chatHistory.length);

      // 清理聊天历史
      cleanupChatHistory();
    }

    // 🔧 确保有一个活跃的session ID
    if (!currentSessionId) {
      createNewSession();
      console.log('应用启动时创建新会话:', currentSessionId);
    }

    // 🔧 从 store 恢复登录状态
    const savedToken = store.get('user.token');
    const savedUserInfo = store.get('user.info');
    if (savedToken && savedUserInfo) {
      appState.isLoggedIn = true;
      appState.token = savedToken;
      appState.userInfo = savedUserInfo;
      console.log('从存储恢复登录状态:', {
        isLoggedIn: true,
        hasToken: !!savedToken,
        userEmail: savedUserInfo.email
      });
    } else {
      console.log('未找到保存的登录状态，保持未登录状态');
    }
  } catch (error) {
    console.error('初始化存储失败:', error);
  }
}

// 清理聊天历史，确保结构正确
function cleanupChatHistory() {
  if (!chatHistory || chatHistory.length === 0) return;

  // 移除无效消息并限制长度
  chatHistory = chatHistory
    .filter(msg => msg && msg.role && msg.content)
    .slice(-20); // 只保留最近20条消息

  if (store) {
    store.set('chatHistory', chatHistory);
  }
}

// **********************************************************************
// * 优化后的登录状态检查处理程序 *
// **********************************************************************
ipcMain.handle('check-login-status', async (event, options = {}) => {
  try {
    // 如果是登录页面，且设置了noAutoLogin或临时的noAutoLogin标志
    if ((options && options.isLoginPage && (options.noAutoLogin || appState.tempNoAutoLogin))) {
      return {
        success: true,
        isLoggedIn: false,
        userInfo: null
      };
    }

    if (!appState.isLoggedIn && store) {
      const token = store.get('user.token');
      const userInfo = store.get('user.info');

      if (token && userInfo && !options.noAutoLogin && !appState.tempNoAutoLogin) {
        appState.isLoggedIn = true;
        appState.token = token;
        appState.userInfo = userInfo;
      }
    }

    return {
      success: true,
      isLoggedIn: appState.isLoggedIn,
      userInfo: appState.userInfo
    };
  } catch (error) {
    console.error('[check-login-status] Error:', error);
    return { success: false, error: error.message };
  }
});

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 900,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    show: false, // 初始化时不显示窗口
    backgroundColor: '#f5f7fa', // 与应用背景色匹配
    titleBarStyle: 'default', // 修改为default以显示标准关闭按钮
    autoHideMenuBar: false // 显示菜单栏
  });

  // 加载登录页面，而不是直接加载index.html
  mainWindow.loadURL(url.format({
    pathname: path.join(__dirname, 'index.html'),
    protocol: 'file:',
    slashes: true
  }));

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 当窗口关闭时触发的事件
  mainWindow.on('closed', function () {
    mainWindow = null;
  });
}

// 当Electron完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(async () => {
  // 先初始化 store
  await initStore();

  // 注册自定义协议处理器
  if (!app.isDefaultProtocolClient('meetinggpt')) {
    app.setAsDefaultProtocolClient('meetinggpt');
  }

  createWindow();
});

// 所有窗口关闭时退出应用
app.on('window-all-closed', function () {
  app.quit();
});

app.on('activate', function () {
  if (mainWindow === null) createWindow();
});

// 处理IPC通信
ipcMain.on('audio-data', async (event, data) => {
  try {
    const prompt = data.prompt || '请解析这段会议音频并提取关键信息';
    console.log('🎯 收到的prompt:', prompt.substring(0, 100) + '...');

    try {
      // 🔍 详细检查用户登录状态
      console.log('🔍 检查用户登录状态:', {
        isLoggedIn: appState.isLoggedIn,
        hasToken: !!appState.token,
        tokenLength: appState.token ? appState.token.length : 0,
        hasUserInfo: !!appState.userInfo
      });

      // 如果未登录，尝试从store恢复登录状态
      if (!appState.isLoggedIn && store) {
        console.log('🔄 尝试从store恢复登录状态...');
        const savedToken = store.get('user.token');
        const savedUserInfo = store.get('user.info');

        if (savedToken && savedUserInfo) {
          appState.isLoggedIn = true;
          appState.token = savedToken;
          appState.userInfo = savedUserInfo;
          console.log('✅ 从store成功恢复登录状态');
        } else {
          console.log('❌ store中未找到有效的登录信息');
        }
      }

      // 最终检查
      if (!appState.isLoggedIn || !appState.token) {
        console.log('❌ 用户未登录，无法进行音频转录');
        throw new Error('用户未登录，无法进行音频转录。请先登录。');
      }

      console.log('✅ 用户已登录，开始音频转录');

      // 步骤1：先将音频转录为文本，传递用户token
      const transcription = await apiClient.transcribeAudio(data, 'whisper-1', 'verbose_json', {}, appState.token);

      // 立即发送转录结果到前端，不等待AI回复
      event.reply('audio-transcription', {
        status: 'success',
        transcription: transcription
      });

      // 🔧 检查转录文本是否为空
      if (!transcription || transcription.trim() === '') {
        console.log('🔇 转录文本为空，跳过chat completion');
        event.reply('audio-response-chunk', {
          status: 'done',
          final: true,
          message: '转录文本为空，未进行AI处理'
        });
        return;
      }

      // 简单检查是否为重复转录
      const isDuplicate = chatHistory.some(msg =>
        msg.role === 'user' && msg.content === transcription
      );

      if (isDuplicate) {
        event.reply('audio-response-chunk', {
          status: 'error',
          error: '检测到重复的语音输入，已跳过处理',
          isDuplicate: true
        });
        return;
      }

      // 步骤2：将转录文本发送给聊天API获取回复
      console.log(`- API地址: ${apiClient.baseUrl}/api/v1/chat/completions`);

      // 构建消息数组，包括历史记录
      const sortedMessages = [
        { role: 'system', content: prompt },
        ...chatHistory.slice(-10), // 只保留最近10条历史消息
        { role: 'user', content: transcription }
      ];

      console.log('🎯 收到的prompt:', prompt.substring(0, 100) + '...');
      console.log('📝 转录文本:', transcription);
      console.log('💬 聊天历史条数:', chatHistory.length);
      console.log('📋 完整请求体消息数组:', JSON.stringify(sortedMessages, null, 2));

      // 收集完整响应的变量
      let fullResponse = '';
      let responseMessage = null;

      // 使用流式响应，并提供onChunk回调函数，传递用户token
      await apiClient.chatCompletion(
        'gpt-3.5-turbo',
        sortedMessages,
        true,
        (chunk) => {
          // 处理每个数据块
          if (chunk.done) {
            console.log('🏁 流式响应完成，准备保存完整对话');

            // 🔧 防止重复保存：检查是否已经完成
            if (isCompletionDone) {
              console.log('🔄 检测到重复的completion完成信号，跳过保存');
              return;
            }
            isCompletionDone = true;

            // 创建消息对象
            const userMessage = { role: 'user', content: transcription };
            const assistantMessage = { role: 'assistant', content: fullResponse };

            // 添加新消息到历史记录
            chatHistory.push(userMessage, assistantMessage);

            // 限制历史记录长度
            if (chatHistory.length > 20) {
              chatHistory = chatHistory.slice(-20);
            }

            // 保存到临时chatHistory（用于当前会话状态管理）
            if (store) {
              store.set('chatHistory', chatHistory);
            }

            // 🎯 只在chat completion完成时保存消息到会话
            saveMessageToSession(userMessage, assistantMessage)
              .then((result) => {
                if (result.success) {
                  console.log('✅ 消息已保存到会话:', result.sessionId);
                } else {
                  console.error('❌ 保存消息失败:', result.message);
                }
              })
              .catch((error) => {
                console.error('❌ 保存消息到会话失败:', error);
              });

            // 发送完成信号到前端
            event.reply('audio-response-chunk', {
              status: 'done',
              final: true
            });

            return;
          }

          if (chunk.error) {
            console.error('流式响应错误:', chunk.error);
            event.reply('audio-response-chunk', {
              status: 'error',
              error: chunk.error
            });
            return;
          }

          try {
            if (chunk.choices && chunk.choices[0].delta) {
              const delta = chunk.choices[0].delta;

              // 如果是新消息，创建消息对象
              if (delta.role && !responseMessage) {
                responseMessage = { role: delta.role, content: '' };
              }

              // 追加内容
              if (delta.content) {
                fullResponse += delta.content;
                if (responseMessage) {
                  responseMessage.content += delta.content;
                }

                // 立即将每个内容块发送到前端，实现流式效果
                // 注意：不在每个chunk中包含transcription，避免重复处理
                event.reply('audio-response-chunk', {
                  status: 'chunk',
                  content: delta.content
                });
              }
            }
          } catch (error) {
            console.error('处理响应块失败:', error);
            event.reply('audio-response-chunk', {
              status: 'error',
              error: error.message
            });
          }
        },
        appState.token
      );

    } catch (transcriptionError) {
      console.error('音频转录失败:', transcriptionError);

      // 使用流式响应格式发送错误
      event.reply('audio-response-chunk', {
        status: 'error',
        error: `转录失败: ${transcriptionError.message}`
      });
    }
  } catch (error) {
    console.error('处理音频数据时出错:', error);

    // 详细记录错误信息
    if (error.cause) {
      console.error('错误原因:', error.cause);
    }

    if (error.code) {
      console.error('错误代码:', error.code);
    }

    // 检查API客户端配置
    console.log('当前API配置:');
    console.log(`- API基础URL: ${apiClient.baseUrl}`);
    console.log(`- 连接状态: ${apiClient.isConnected ? '已连接' : '未连接'}`);
    console.log(`- 连接类型: ${apiClient.connectionType || '未设置'}`);

    // 使用流式响应格式发送错误
    event.reply('audio-response-chunk', {
      status: 'error',
      error: `处理失败: ${error.message}`
    });
  }
});

// 处理页面切换
ipcMain.on('navigate', (_, page) => {
  // 在导航前保存当前录音状态到store
  if (store) {
    store.set('recordingState', recordingState);
    store.set('chatHistory', chatHistory);
    // console.log('导航前已保存录音状态和聊天历史');
  }

  if (mainWindow) {
    // 解析页面路径和查询参数
    let pagePath = page;
    let queryParams = '';
    let parsedParams = {};

    if (page.includes('?')) {
      const parts = page.split('?');
      pagePath = parts[0];
      queryParams = `?${parts[1]}`;

      // 解析URL参数到对象
      const searchParams = new URLSearchParams(parts[1]);
      for (const [key, value] of searchParams.entries()) {
        parsedParams[key] = value;
      }
    }

    // console.log(`导航到页面: ${pagePath}${queryParams ? `, 参数: ${JSON.stringify(parsedParams)}` : ''}`);

    // 如果是登录页面且有noAutoLogin参数，向渲染进程发送标志
    if (pagePath === 'login.html' && parsedParams.noAutoLogin === 'true') {
      console.log('登录页面: 设置noAutoLogin标志');
      appState.tempNoAutoLogin = true;
    } else {
      appState.tempNoAutoLogin = false;
    }

    mainWindow.loadURL(url.format({
      pathname: path.join(__dirname, pagePath),
      protocol: 'file:',
      slashes: true,
      search: queryParams
    }));

    // 在页面加载完成后发送事件通知前端
    mainWindow.webContents.once('did-finish-load', () => {
      // 如果是登录页面，传递noAutoLogin参数
      if (pagePath === 'login.html' && appState.tempNoAutoLogin) {
        mainWindow.webContents.send('login-page-loaded', { noAutoLogin: true });
      }
      mainWindow.webContents.send('page-loaded');
    });
  }
});


// 修改 save-recording-state IPC 处理器
ipcMain.on('save-recording-state', (event, state) => {
  if (store) {
    if (state.hasOwnProperty('audioDeviceId')) {
      recordingState.audioDeviceId = state.audioDeviceId;
    }
    if (state.hasOwnProperty('silenceThreshold')) {
      recordingState.silenceThreshold = state.silenceThreshold;
    }
    if (state.hasOwnProperty('silenceDuration')) {
      recordingState.silenceDuration = state.silenceDuration;
    }
    if (state.hasOwnProperty('silence_counter_threshold')) {
      recordingState.silence_counter_threshold = state.silence_counter_threshold;
    }
    // recordingState.isRecording 不在此处通过state参数更新，由专门的IPC控制

    store.set('recordingState', recordingState);
  } else {
    console.warn('主进程：存储尚未初始化，录音相关设置仅在内存中更新。');
  }
  event.reply('save-recording-state-response', { status: 'success' });
});

// 新增：处理用户开始录音的IPC消息
ipcMain.on('user-started-recording', () => {
  if (recordingState.isRecording === false) {
    recordingState.isRecording = true;
    if (store) {
      store.set('recordingState', recordingState);
    }
  }

  // 🔧 开始录音时不清除聊天历史，保持当前显示的消息
  console.log('开始录音：保持当前聊天历史');

  // 🔧 修复：开始录音时不创建新会话，保持当前session的连续性
  // 只有在没有活跃session时才创建新session
  if (!currentSessionId) {
    createNewSession();
    console.log('没有活跃session，创建新会话:', currentSessionId);
  } else {
    console.log('继续使用当前session:', currentSessionId);
  }
  console.log('当前聊天历史条数:', chatHistory.length);
});

// 新增：处理用户停止录音的IPC消息
ipcMain.on('user-stopped-recording', () => {
  if (recordingState.isRecording === true) {
    recordingState.isRecording = false;
    if (store) {
      store.set('recordingState', recordingState);
    }
  }
});

// 获取录音状态
ipcMain.handle('get-recording-state', async (event) => {
  // console.log('获取录音状态:', recordingState);
  return recordingState;
});

// 获取聊天历史
ipcMain.handle('get-chat-history', async (event) => {
  // console.log('获取聊天历史, 条数:', chatHistory.length);
  return chatHistory;
});

// 清除聊天历史
ipcMain.handle('clear-chat-history', async (event) => {
  chatHistory = [];
  if (store) {
    store.set('chatHistory', chatHistory);
  }
  console.log('聊天历史已清除');
  return { success: true };
});

// 获取聊天会话列表
ipcMain.handle('get-chat-sessions', async (event) => {
  try {
    const sessions = store ? store.get('chatSessions', []) : [];
    console.log(`获取聊天会话列表, 共 ${sessions.length} 个会话`);
    return sessions;
  } catch (error) {
    console.error('获取聊天会话列表失败:', error);
    return [];
  }
});

// 简化的会话管理
let currentSessionId = null;
let isCompletionDone = false; // 防止重复保存

// 简单的保存函数：只在chat completion完成时追加消息
async function saveMessageToSession(userMessage, assistantMessage) {
  try {
    if (!currentSessionId) {
      console.log('❌ 没有活跃的会话ID，无法保存消息');
      return { success: false, message: '没有活跃的会话' };
    }

    const sessions = store ? store.get('chatSessions', []) : [];

    // 查找当前会话
    let currentSession = sessions.find(s => s.id === currentSessionId);

    if (!currentSession) {
      // 如果会话不存在，创建新会话
      const preview = userMessage.content.substring(0, 100);
      currentSession = {
        id: currentSessionId,
        title: `Session ${sessions.length + 1}`,
        preview: preview,
        messages: [userMessage, assistantMessage], // 直接设置消息
        messageCount: 2,
        createdAt: new Date().toISOString(),
        timestamp: Date.now()
      };
      sessions.unshift(currentSession); // 添加到开头
      console.log(`📝 创建新会话: ${currentSessionId}`);
    } else {
      // 追加消息到现有会话
      currentSession.messages.push(userMessage, assistantMessage);
      currentSession.messageCount = currentSession.messages.length;
      console.log(`📝 追加消息到会话: ${currentSessionId}, 总计: ${currentSession.messageCount}`);
    }

    // 限制会话数量（最多保存50个会话）
    if (sessions.length > 50) {
      sessions.splice(50);
    }

    if (store) {
      store.set('chatSessions', sessions);
    }

    console.log(`✅ 已保存消息到会话: ${currentSessionId}, 用户: "${userMessage.content.substring(0, 30)}...", AI: "${assistantMessage.content.substring(0, 30)}..."`);
    return { success: true, sessionId: currentSessionId };
  } catch (error) {
    console.error('保存消息到会话失败:', error);
    return { success: false, message: error.message };
  }
}

// 创建新会话
function createNewSession() {
  currentSessionId = Date.now().toString();
  isCompletionDone = false; // 重置完成标志
  console.log(`🆕 创建新会话ID: ${currentSessionId}`);
  return currentSessionId;
}

// 🔧 新增：处理新建对话操作
ipcMain.handle('new-conversation', async (event) => {
  try {
    // 清空聊天历史
    chatHistory = [];
    if (store) {
      store.set('chatHistory', chatHistory);
    }

    // 创建新会话
    createNewSession();

    console.log('🆕 新建对话：已清空聊天历史并创建新会话');
    return { success: true, sessionId: currentSessionId };
  } catch (error) {
    console.error('新建对话失败:', error);
    return { success: false, message: error.message };
  }
});

// 保存当前聊天为新会话（IPC处理器）- 已废弃
ipcMain.handle('save-current-chat-as-session', async (event) => {
  console.log('⚠️ save-current-chat-as-session已废弃，消息会自动保存');
  return { success: false, message: '已废弃，消息会自动保存' };
});

// 加载特定会话
ipcMain.handle('load-chat-session', async (event, sessionId) => {
  try {
    const sessions = store ? store.get('chatSessions', []) : [];
    const session = sessions.find(s => s.id === sessionId);

    if (!session) {
      return { success: false, message: '会话不存在' };
    }

    // 将会话消息设置为当前聊天历史
    chatHistory = [...session.messages];

    // 🔧 修复：设置当前session ID，确保后续消息保存到正确的会话
    currentSessionId = sessionId;
    isCompletionDone = false; // 重置完成标志

    if (store) {
      store.set('chatHistory', chatHistory);
    }

    console.log(`已加载聊天会话: ${sessionId}, 消息数: ${chatHistory.length}`);
    console.log(`当前活跃session ID: ${currentSessionId}`);
    return { success: true, messages: chatHistory };
  } catch (error) {
    console.error('加载聊天会话失败:', error);
    return { success: false, message: error.message };
  }
});

// 删除会话
ipcMain.handle('delete-chat-session', async (event, sessionId) => {
  try {
    const sessions = store ? store.get('chatSessions', []) : [];
    const sessionIndex = sessions.findIndex(s => s.id === sessionId);

    if (sessionIndex === -1) {
      return { success: false, message: '会话不存在' };
    }

    sessions.splice(sessionIndex, 1);

    if (store) {
      store.set('chatSessions', sessions);
    }

    console.log(`已删除聊天会话: ${sessionId}`);
    return { success: true };
  } catch (error) {
    console.error('删除聊天会话失败:', error);
    return { success: false, message: error.message };
  }
});

// 处理设置API URL
ipcMain.on('set-api-url', (event, url) => {
  if (!url) return;

  console.log(`正在更新API基础URL: ${url}`);

  // 使用api.js中新增的setBaseUrl方法设置基础URL
  const success = apiClient.setBaseUrl(url);

  if (success) {
    event.reply('set-api-url-response', {
      status: 'success',
      message: `API基础URL已更新为: ${url}`
    });
  } else {
    event.reply('set-api-url-response', {
      status: 'error',
      message: '设置API基础URL失败'
    });
  }
});

// 获取用户信息
ipcMain.handle('get-user-info', async (event, options = {}) => {
  console.log('get-user-info 被调用，options:', options);

  try {
    // 如果是从localStorage获取模式，直接返回成功
    if (options && options.fromLocalStorage) {
      console.log('使用localStorage模式');
      return { success: true, message: '使用本地存储管理用户信息' };
    }

    // 直接检查登录状态逻辑
    // console.log('检查登录状态...');

    // 如果是登录页面，且设置了noAutoLogin或临时的noAutoLogin标志
    if ((options && options.isLoginPage && (options.noAutoLogin || appState.tempNoAutoLogin))) {
      console.log('登录页面且设置了noAutoLogin，返回未登录状态');
      return { success: false, message: '用户未登录' };
    }

    // 检查appState中的登录状态
    if (!appState.isLoggedIn && store) {
      const token = store.get('user.token');
      const userInfo = store.get('user.info');
      console.log('从store检查登录状态:', { hasToken: !!token, hasUserInfo: !!userInfo });

      if (token && userInfo && !options.noAutoLogin && !appState.tempNoAutoLogin) {
        appState.isLoggedIn = true;
        appState.token = token;
        appState.userInfo = userInfo;
        console.log('从store恢复登录状态');
      }
    }

    console.log('当前登录状态:', {
      isLoggedIn: appState.isLoggedIn,
      hasToken: !!appState.token,
      hasUserInfo: !!appState.userInfo
    });

    if (!appState.isLoggedIn) {
      console.log('用户未登录');
      return { success: false, message: '用户未登录' };
    }

    // 如果强制使用localStorage模式，跳过API调用
    if (options && options.useLocalStorageOnly) {
      console.log('使用localStorage Only模式');
      return {
        success: true,
        isLoggedIn: appState.isLoggedIn,
        data: appState.userInfo
      };
    }

    // 尝试从API获取最新用户信息
    try {
      const token = appState.token;
      console.log('获取到的token:', token ? '存在' : '不存在');
      console.log('token前几位:', token ? token.substring(0, 10) + '...' : 'null');

      if (!token) {
        console.log('没有token，用户未登录');
        return { success: false, message: '没有有效的认证令牌' };
      }

      console.log('调用apiClient.getUserInfo...');
      console.log('apiClient状态:', {
        baseUrl: apiClient.baseUrl,
        hasGetUserInfo: typeof apiClient.getUserInfo === 'function'
      });

      const result = await apiClient.getUserInfo(token);
      // console.log('API调用成功，结果:', result);

      // 更新缓存的用户信息
      appState.userInfo = result;
      appState.isLoggedIn = true;

      // 保存到store
      if (store) {
        store.set('user.info', result);
        console.log('用户信息已保存到store');
      }

      return { success: true, data: result };
    } catch (error) {
      console.error('API调用失败:', error);
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });

      // API调用失败，如果是token无效错误，清除用户状态
      if (error.message.includes('token') || error.message.includes('unauthorized') || error.message.includes('认证')) {
        console.log('token无效，清除用户状态');
        appState.isLoggedIn = false;
        appState.userInfo = null;
        appState.token = null;

        if (store) {
          store.delete('user.token');
          store.delete('user.info');
        }
      }

      return { success: false, message: error.message };
    }
  } catch (error) {
    console.error('get-user-info 处理函数出错:', error);
    return { success: false, message: error.message };
  }
});

// 使用真实数据进行用户认证
async function userLogin(email, password) {
  try {
    const result = await apiClient.login(email, password);
    return result; // 返回包含token和用户信息的对象
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
}

// 用户注册
async function userRegister(email, password, verificationCode) {
  try {
    const result = await apiClient.register(email, password, verificationCode);
    return result;
  } catch (error) {
    console.error('注册失败:', error);
    throw error;
  }
}

// 处理用户登录请求
ipcMain.handle('user-login', async (event, credentials) => {
  try {
    const { email, password } = credentials;
    console.log(`尝试用户登录: ${email}`);

    const result = await userLogin(email, password);
    console.log('登录成功，用户:', result.user.email);

    // 保存登录状态和token
    appState.isLoggedIn = true;
    appState.token = result.token;
    appState.userInfo = result.user;

    // 如果存在store，保存token到本地存储
    if (store) {
      store.set('user.token', result.token);
      store.set('user.info', result.user);
    }

    return { success: true, data: result };
  } catch (error) {
    console.error('登录失败:', error);
    return { success: false, error: error.message };
  }
});

// 处理用户注册请求
ipcMain.handle('user-register', async (event, userData) => {
  try {
    const { email, password, verification_code } = userData;
    console.log(`尝试用户注册: ${email}`);

    const result = await userRegister(email, password, verification_code);
    console.log('注册成功，消息:', result.message);

    return { success: true, data: result };
  } catch (error) {
    console.error('注册失败:', error);
    return { success: false, error: error.message };
  }
});

// 处理获取验证码请求
ipcMain.handle('get-verification-code', async (event, data) => {
  try {
    const { email, type } = data;
    console.log(`尝试获取验证码: ${email}, 类型: ${type}`);

    const result = await apiClient.getVerificationCode(email, type);
    console.log('验证码发送成功');

    if (result && result.success) {
      return { success: true, message: '验证码已发送，请检查您的邮箱' };
    } else {
      return { success: false, message: result.message || '获取验证码失败' };
    }
  } catch (error) {
    console.error('获取验证码失败:', error);
    return { success: false, error: error.message };
  }
});

// 处理修改密码请求
ipcMain.handle('change-password', async (event, data) => {
  try {
    const { email, new_password, verification_code } = data;
    console.log(`尝试修改密码: ${email}`);

    // 获取token，首先从appState取，如果没有则从store取
    const token = appState.token || (store ? store.get('user.token') : null);

    if (!token) {
      throw new Error('未登录，无法修改密码');
    }

    const result = await apiClient.changePassword(email, new_password, verification_code, token);
    console.log('密码修改成功');

    return { success: true, data: result };
  } catch (error) {
    console.error('修改密码失败:', error);
    return { success: false, error: error.message };
  }
});

// 处理用户注销
ipcMain.handle('logout-user', async (event, options = {}) => {
  const prevUser = appState.userInfo;

  // 清除用户信息
  appState.isLoggedIn = false; // 确保设置登录状态为false
  appState.userInfo = null;
  appState.token = null;

  // 如果传递了强制清除参数，则从存储中完全删除用户信息
  if (options && options.forceClearState && store) {
    store.delete('user.token');
    store.delete('user.info');
    console.log('已从持久化存储中删除用户信息');
  } else {
    saveAppState();
  }

  // 向所有渲染进程广播注销事件
  BrowserWindow.getAllWindows().forEach(win => {
    win.webContents.send('logout-success');
  });

  console.log('用户已注销:', prevUser?.email, '强制清除状态:', options?.forceClearState);
  return { success: true };
});

// 获取环境变量
ipcMain.handle('get-env-var', async (event, name) => {
  try {
    const value = process.env[name] || '';
    return { success: true, value };
  } catch (error) {
    console.error(`获取环境变量失败: ${name}`, error);
    return { success: false, error: error.message };
  }
});

// 打开网页端登录页面
ipcMain.handle('open-web-login', async (event) => {
  try {
    console.log('正在打开网页端登录页面...');

    // 从环境变量获取网站URL
    const websiteUrl = process.env.WEBSITE_URL || 'http://localhost:3000';

    // 构建登录URL，包含必要的参数
    const loginUrl = new URL('/login', websiteUrl);

    // 添加重定向参数
    loginUrl.searchParams.append('redirect_uri', 'meetinggpt://auth');

    // 添加客户端ID
    loginUrl.searchParams.append('client_id', 'meetinggpt-desktop');

    // 添加随机状态值，用于防止CSRF攻击
    const state = Date.now().toString(36) + Math.random().toString(36).substring(2);
    loginUrl.searchParams.append('state', state);

    // 添加响应类型
    loginUrl.searchParams.append('response_type', 'code');

    console.log('登录URL:', loginUrl.toString());

    // 使用shell模块打开浏览器
    const { shell } = require('electron');
    await shell.openExternal(loginUrl.toString());

    return { success: true };
  } catch (error) {
    console.error('打开网页端登录页面失败:', error);
    return { success: false, error: error.message };
  }
});



// 处理自定义URL协议
app.on('open-url', (event, url) => {
  event.preventDefault();
  console.log('收到自定义URL:', url);

  // 处理meetinggpt://协议
  if (url.startsWith('meetinggpt://')) {
    console.log('处理meetinggpt协议URL:', url);
    // 可以在这里添加其他URL处理逻辑
  }

  // 处理meetinggpt://auth协议 (用于OAuth回调)
  if (url.startsWith('meetinggpt://auth')) {
    console.log('处理OAuth回调URL:', url);

    try {
      // 解析URL，提取授权码
      const urlObj = new URL(url);
      const code = urlObj.searchParams.get('code');
      const state = urlObj.searchParams.get('state');

      if (code) {
        console.log('获取到授权码:', code);
        // 使用授权码交换访问令牌
        exchangeAuthCodeForToken(code, state);
      } else {
        console.error('OAuth回调URL中没有授权码');
      }
    } catch (error) {
      console.error('处理OAuth回调URL时出错:', error);
    }
  }
});

// 使用授权码获取用户信息
async function exchangeAuthCodeForToken(code) {
  try {
    console.log('正在使用授权码获取用户信息...');

    // 从环境变量获取API基础URL
    const apiBaseUrl = process.env.API_BASE_URL || 'http://localhost:8082';

    // 直接使用授权码作为Bearer令牌获取用户信息
    const response = await fetch(`${apiBaseUrl}/api/v1/user/info`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${code}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `获取用户信息失败: ${response.status}`);
    }

    const userData = await response.json();
    console.log('成功获取用户信息:', userData);

    // 保存用户信息和令牌
    appState.isLoggedIn = true;
    appState.token = code; // 直接使用授权码作为令牌
    appState.userInfo = userData;

    // 保存到store
    if (store) {
      store.set('user.token', code);
      store.set('user.info', userData);
    }

    // 如果主窗口存在，通知渲染进程登录成功
    if (mainWindow) {
      mainWindow.webContents.send('oauth-login-success', {
        user: userData,
        token: code
      });

      // 导航到主页
      mainWindow.loadURL(url.format({
        pathname: path.join(__dirname, 'index.html'),
        protocol: 'file:',
        slashes: true
      }));
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);

    // 通知渲染进程登录失败
    if (mainWindow) {
      mainWindow.webContents.send('oauth-login-error', {
        error: error.message
      });
    }
  }
}

// 处理语言设置的保存和加载
ipcMain.handle('get-setting', async (event, key) => {
  try {
    if (!store) {
      return null;
    }
    const value = store.get(`settings.${key}`);
    return value;
  } catch (error) {
    console.error(`获取设置失败: ${key}`, error);
    return null;
  }
});

ipcMain.handle('save-setting', async (event, key, value) => {
  try {
    if (!store) {
      return { success: false, error: 'Store not initialized' };
    }
    store.set(`settings.${key}`, value);
    return { success: true };
  } catch (error) {
    console.error(`保存设置失败: ${key}`, error);
    return { success: false, error: error.message };
  }
});