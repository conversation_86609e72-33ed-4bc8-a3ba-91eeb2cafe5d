<!DOCTYPE html>
<html data-i18n-title="settings.title">
<head>
  <meta charset="UTF-8">
  <title>Settings - MeetingGPT</title>
  <style>
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f7fa;
      color: #2c3e50;
    }

    .header {
      background-color: #1a73e8;
      color: white;
      padding: 0;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .navbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 10px 20px;
    }

    .logo {
      display: flex;
      align-items: center;
    }

    .logo h1 {
      margin: 0;
      font-size: 1.6em;
      font-weight: 500;
    }

    .navigation {
      display: flex;
      align-items: center;
    }

    .nav-link {
      color: white;
      text-decoration: none;
      margin: 0 15px;
      padding: 5px 0;
      position: relative;
      font-weight: 500;
    }

    .nav-link::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0;
      height: 2px;
      background-color: white;
      transition: width 0.3s;
    }

    .nav-link:hover::after, .nav-link.active::after {
      width: 100%;
    }

    .nav-link.active {
      font-weight: bold;
    }

    .login-button {
      background-color: white;
      color: #1a73e8;
      padding: 8px 16px;
      border-radius: 20px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s;
    }

    .login-button:hover {
      background-color: #f0f7ff;
      transform: translateY(-2px);
    }

    .container {
      max-width: 1000px;
      margin: 30px auto;
      padding: 0 20px;
    }

    .settings-container {
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      overflow: hidden;
    }

    .settings-tabs {
      display: flex;
      background-color: #f8f9fa;
      border-bottom: 1px solid #e1e4e8;
    }

    .settings-tab {
      padding: 15px 25px;
      font-weight: 600;
      cursor: pointer;
      position: relative;
      color: #718096;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .settings-tab.active {
      color: #1a73e8;
      background-color: white;
    }

    .settings-tab.active:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background-color: #1a73e8;
    }

    .settings-content {
      padding: 30px;
    }

    .settings-panel {
      display: none;
    }

    .settings-panel.active {
      display: block;
    }

    h2 {
      color: #1a73e8;
      font-size: 1.5em;
      margin-top: 0;
      margin-bottom: 25px;
      padding-bottom: 12px;
      border-bottom: 2px solid #e1e4e8;
      display: flex;
      align-items: center;
    }

    h3 {
      font-size: 1.2em;
      color: #2c3e50;
      margin-top: 30px;
      margin-bottom: 15px;
    }

    .settings h2 .icon {
      margin-right: 8px;
      font-size: 20px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      color: #4a5568;
      font-weight: 500;
    }

    select, input, textarea {
      width: 100%;
      padding: 10px 12px;
      border-radius: 6px;
      border: 1px solid #ddd;
      background-color: #f8f9fa;
      font-size: 15px;
      transition: all 0.3s ease;
    }

    select:focus, input:focus, textarea:focus {
      border-color: #1a73e8;
      outline: none;
      box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.15);
      background-color: #fff;
    }

    textarea {
      min-height: 120px;
      resize: vertical;
    }

    button {
      background-color: #1a73e8;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      transition: all 0.3s;
      display: flex;
      align-items: center;
    }

    button:hover {
      background-color: #0d64d6;
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
    }

    button:disabled {
      background-color: #bdc3c7;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .visualizer-container {
      width: 100%;
      height: 80px;
      background-color: #2c3e50;
      margin-bottom: 25px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    #visualizer {
      width: 100%;
      height: 100%;
    }

    .custom-prompts-list {
      margin-top: 20px;
      border: 1px solid #e1e4e8;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .custom-prompt-item {
      padding: 12px 15px;
      border-bottom: 1px solid #e1e4e8;
      display: flex;
      align-items: center;
      transition: all 0.3s ease;
      cursor: pointer;
      border-left: 3px solid transparent;
    }

    .custom-prompt-item:last-child {
      border-bottom: none;
    }

    .custom-prompt-item:hover {
      background-color: #f6f8fa;
      transform: translateX(2px);
    }

    .custom-prompt-item.selected {
      background-color: #e3f2fd;
      border-left-color: #1a73e8;
      box-shadow: 0 2px 8px rgba(26, 115, 232, 0.15);
    }

    .custom-prompt-item.selected .custom-prompt-name {
      color: #1a73e8;
      font-weight: 600;
    }

    .custom-prompt-name {
      flex: 1;
      font-weight: 500;
    }

    .custom-prompt-actions {
      display: flex;
    }

    .custom-prompt-action {
      background-color: transparent;
      border: none;
      color: #718096;
      cursor: pointer;
      padding: 5px;
      margin: 0 5px;
      font-size: 14px;
      transition: color 0.3s;
    }

    .custom-prompt-action:hover {
      color: #1a73e8;
    }

    .delete-action:hover {
      color: #e53935;
    }

    .add-custom-prompt {
      display: flex;
      margin-top: 15px;
    }

    .add-custom-prompt button {
      display: inline-flex;
      align-items: center;
    }

    .add-custom-prompt button .icon {
      margin-right: 8px;
      font-size: 18px;
    }

    .custom-prompt-form {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      margin-top: 20px;
      border: 1px solid #e1e4e8;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .form-buttons {
      display: flex;
      justify-content: flex-end;
      margin-top: 15px;
      gap: 10px;
    }

    .save-button {
      text-align: right;
      margin-top: 30px;
    }

    .save-button button {
      padding: 12px 25px;
    }

    .help-info {
      background-color: #fff8e1;
      border-radius: 8px;
      padding: 15px;
      margin-top: 25px;
      border-left: 4px solid #ffca28;
      line-height: 1.5;
    }

    .help-info p {
      margin-top: 0;
      font-weight: 500;
    }

    .help-info ol {
      margin-bottom: 0;
      padding-left: 20px;
    }

    .help-info li {
      margin-bottom: 5px;
    }

    .help-info li:last-child {
      margin-bottom: 0;
    }

    .subscription-info {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border: 1px solid #dee2e6;
      border-radius: 12px;
      padding: 20px;
      margin-top: 25px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
    }

    .subscription-info:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      transform: translateY(-1px);
    }

    .subscription-card {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .subscription-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    .plan-badge {
      display: inline-flex;
      align-items: center;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .plan-badge.free {
      background-color: #e3f2fd;
      color: #1976d2;
      border: 1px solid #bbdefb;
    }

    .plan-badge.monthly {
      background-color: #e8f5e9;
      color: #2e7d32;
      border: 1px solid #c8e6c9;
    }

    .plan-badge.yearly {
      background-color: #fff3e0;
      color: #f57c00;
      border: 1px solid #ffcc02;
    }

    .plan-badge.paid {
      background-color: #f3e5f5;
      color: #7b1fa2;
      border: 1px solid #e1bee7;
    }

    .subscription-details {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .detail-row:last-child {
      border-bottom: none;
    }

    .detail-label {
      font-weight: 500;
      color: #495057;
      font-size: 14px;
    }

    .detail-value {
      font-weight: 600;
      color: #212529;
      font-size: 14px;
    }

    .quota-progress {
      margin-top: 8px;
    }

    .quota-bar {
      width: 100%;
      height: 8px;
      background-color: #e9ecef;
      border-radius: 4px;
      overflow: hidden;
      margin-top: 4px;
    }

    .quota-fill {
      height: 100%;
      background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
      border-radius: 4px;
      transition: width 0.3s ease;
    }

    .quota-text {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #6c757d;
      margin-top: 4px;
    }

    .subscription-actions {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }

    .upgrade-link {
      display: inline-flex;
      align-items: center;
      padding: 8px 16px;
      background: linear-gradient(135deg, #1a73e8 0%, #0d64d6 100%);
      color: white;
      text-decoration: none;
      border-radius: 6px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(26, 115, 232, 0.2);
    }

    .upgrade-link:hover {
      background: linear-gradient(135deg, #0d64d6 0%, #0b5bb8 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(26, 115, 232, 0.3);
      text-decoration: none;
    }

    .upgrade-link.manage {
      background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    }

    .upgrade-link.manage:hover {
      background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    }

    .footer {
      text-align: center;
      padding: 20px 0;
      color: #718096;
      font-size: 0.9em;
      margin-top: 40px;
    }

    .footer-links {
      display: flex;
      justify-content: center;
      margin-bottom: 10px;
    }

    .footer-link {
      color: #1a73e8;
      margin: 0 15px;
      text-decoration: none;
    }

    .footer-link:hover {
      text-decoration: underline;
    }

    /* 用户信息样式 */
    .user-profile {
      display: flex;
      align-items: center;
      margin-left: 20px;
      position: relative;
      padding: 5px 10px;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      transition: background-color 0.3s;
    }

    .user-profile:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .user-profile .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #fff;
      color: #1a73e8;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: bold;
      font-size: 14px;
      cursor: pointer;
    }

    .user-profile .dropdown {
      position: absolute;
      top: 100%;
      right: 0;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      width: 180px;
      z-index: 1000;
      display: none;
      margin-top: 10px;
      overflow: hidden;
    }

    .user-profile .dropdown.active {
      display: block;
    }

    .user-profile .dropdown-item {
      padding: 12px 15px;
      color: #333;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .user-profile .dropdown-item:hover {
      background-color: #f5f7fa;
    }

    .user-profile .dropdown-item.logout {
      border-top: 1px solid #eee;
      color: #e74c3c;
      font-weight: bold;
    }

    .user-profile .dropdown-item.logout:hover {
      background-color: #ffeeee;
    }

    .user-email {
      margin-right: 10px;
      font-size: 14px;
      max-width: 150px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    /* 滑块样式美化 */
    input[type="range"] {
      -webkit-appearance: none;
      appearance: none;
      height: 8px;
      background: #e1e4e8;
      border-radius: 4px;
      outline: none;
    }

    input[type="range"]::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: #1a73e8;
      cursor: pointer;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      transition: background 0.15s ease;
    }

    input[type="range"]::-webkit-slider-thumb:hover {
      background: #0d64d6;
    }

    /* 确保用户信息和登录按钮容器与导航栏对齐 */
    #userProfileContainer, #loginButtonContainer {
      display: flex;
      align-items: center;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
      .settings-tabs {
        flex-direction: column;
      }

      .settings-tab {
        padding: 12px 20px;
      }

      .settings-content {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="navbar">
      <div class="logo">
        <h1 data-i18n="app.name">MeetingGPT</h1>
      </div>
      <div class="navigation">
        <a href="index.html" class="nav-link" data-i18n="navigation.home">Home</a>
        <a href="settings.html" class="nav-link active" data-i18n="navigation.settings">Settings</a>
        <a href="subscription.html" class="nav-link" data-i18n="navigation.subscription">Subscription</a>
        <a class="nav-link" id="userGuideLink" data-i18n="navigation.userGuide">User Guide</a>

        <!-- 用户信息区域（登录后显示） -->
        <div id="userProfileContainer" style="display: none;">
          <div class="user-profile">
            <span class="user-email" id="userEmail"></span>
            <div class="avatar" id="userAvatar"></div>
            <div class="dropdown" id="userDropdown">
              <div class="dropdown-item" data-i18n="user.profile">Profile Settings</div>
              <div class="dropdown-item" data-i18n="user.history">History</div>
              <div class="dropdown-item logout" id="logoutButton" data-i18n="navigation.logout">Logout</div>
            </div>
          </div>
        </div>

        <!-- 登录按钮（未登录时显示） -->
        <div id="loginButtonContainer" style="display: none;">
          <a href="login.html" class="login-button" data-i18n="navigation.login">Login/Register</a>
        </div>
      </div>
    </div>
  </div>

  <div class="container">
    <div class="settings-container">
      <div class="settings-tabs">
        <div class="settings-tab active" data-tab="audio-settings">
          <span class="icon">🎤</span> <span data-i18n="settings.audio.title">Audio Settings</span>
        </div>
        <div class="settings-tab" data-tab="ai-settings">
          <span class="icon">🤖</span> <span data-i18n="settings.ai.title">AI Assistant Settings</span>
        </div>
        <div class="settings-tab" data-tab="language-settings">
          <span class="icon">🌐</span> <span data-i18n="settings.language.title">Language Settings</span>
        </div>
      </div>

      <div class="settings-content">
        <div class="settings-panel active" id="audio-settings">
          <h2 data-i18n="settings.audio.title">Audio Settings</h2>

          <div class="visualizer-container">
            <canvas id="visualizer"></canvas>
          </div>

          <div class="form-group">
            <label for="audioDevices" data-i18n="settings.audio.microphone">Microphone Device:</label>
            <select id="audioDevices">
              <option value="" data-i18n="settings.audio.loading">Loading...</option>
            </select>
          </div>

          <div class="form-group">
            <label for="silenceThreshold" data-i18n="settings.audio.silenceThreshold">Silence Threshold (0-1):</label>
            <input type="range" id="silenceThreshold" min="0" max="1" step="0.01" value="0.05">
          </div>

          <div class="form-group">
            <label for="silenceDuration" data-i18n="settings.audio.silenceDuration">Silence Detection Duration (ms):</label>
            <input type="number" id="silenceDuration" min="500" max="5000" step="100" value="1500">
          </div>

          <div class="help-info">
            <p><strong data-i18n="settings.audio.systemAudioTitle">How to capture system audio:</strong></p>
            <ol>
              <li data-i18n="settings.audio.step1">Make sure Blackhole or similar virtual audio device is installed</li>
              <li data-i18n="settings.audio.step2">In system sound settings, set Blackhole as output device</li>
              <li data-i18n="settings.audio.step3">Or create multi-output device (Blackhole + Speakers)</li>
              <li data-i18n="settings.audio.step4">Select Blackhole as input source in the dropdown menu</li>
            </ol>
          </div>

          <div class="subscription-info">
            <span data-i18n="settings.subscription.loading">Loading subscription information...</span>
          </div>
        </div>

        <div class="settings-panel" id="ai-settings">
          <h2 data-i18n="settings.ai.title">AI Assistant Settings</h2>

          <h3 data-i18n="settings.ai.customPromptManagement">Assistant Management</h3>
          <div class="custom-prompts-list" id="customPromptsList">
            <!-- 助手列表将在这里动态加载 -->
          </div>

          <div class="add-custom-prompt">
            <button id="addCustomPromptBtn">
              <span class="icon">+</span><span data-i18n="settings.ai.addNewPrompt">Add New Assistant</span>
            </button>
          </div>

          <div id="customPromptForm" class="custom-prompt-form" style="display: none;">
            <div class="form-group">
              <label for="customPromptName" data-i18n="settings.ai.promptName">Assistant Name:</label>
              <input type="text" id="customPromptName" data-i18n="settings.ai.promptNamePlaceholder" placeholder="e.g., Translation Assistant">
            </div>

            <div class="form-group">
              <label for="customPrompt" data-i18n="settings.ai.promptContent">Assistant Instructions:</label>
              <textarea id="customPrompt" data-i18n="settings.ai.promptContentPlaceholder" placeholder="Enter assistant instructions, e.g., You are a professional translator, please translate the content I provide..."></textarea>
            </div>

            <div class="form-buttons">
              <button id="cancelCustomPromptBtn" data-i18n="common.cancel">Cancel</button>
              <button id="saveCustomPromptBtn" data-i18n="common.save">Save</button>
            </div>
          </div>
        </div>

        <div class="settings-panel" id="language-settings">
          <h2 data-i18n="settings.language.title">Language Settings</h2>

          <div class="form-group">
            <label for="languageSelect" data-i18n="settings.language.select">Select Language:</label>
            <select id="languageSelect">
              <option value="zh-CN">中文</option>
              <option value="en-US">English</option>
            </select>
          </div>

          <div class="language-info">
            <p><span data-i18n="settings.language.current">Current Language</span>: <span id="currentLanguageDisplay">English</span></p>
            <p><small data-i18n="settings.language.changeNote">Language changes will take effect immediately.</small></p>
          </div>
        </div>
      </div>

      <div class="save-button">
        <button id="saveSettingsBtn" data-i18n="settings.save">Save Settings</button>
      </div>
    </div>
  </div>

  <div class="footer">
    <div class="footer-links">
      <a href="#" class="footer-link" data-i18n="footer.about">About Us</a>
      <a href="#" class="footer-link" data-i18n="footer.terms">Terms of Service</a>
      <a href="#" class="footer-link" data-i18n="footer.privacy">Privacy Policy</a>
      <a class="footer-link" id="footerUserGuideLink" data-i18n="navigation.userGuide">User Guide</a>
      <a href="#" class="footer-link" data-i18n="footer.faq">FAQ</a>
      <a href="mailto:<EMAIL>" class="footer-link" data-i18n="footer.contact">Contact Us</a>
    </div>
    <p><span data-i18n="footer.copyright">© 2025 MeetingGPT. All rights reserved.</span> | <span data-i18n="footer.email">Contact Email</span>: <a href="mailto:<EMAIL>" style="color: #1a73e8;"><EMAIL></a></p>
  </div>

  <script src="i18n.js"></script>
  <script src="languageSwitcher.js"></script>
  <script src="settings.js"></script>
  <script>
    // 选项卡切换功能
    document.addEventListener('DOMContentLoaded', function() {
      const tabs = document.querySelectorAll('.settings-tab');

      tabs.forEach(tab => {
        tab.addEventListener('click', function() {
          // 移除所有选项卡和面板的活动状态
          tabs.forEach(t => t.classList.remove('active'));
          document.querySelectorAll('.settings-panel').forEach(p => p.classList.remove('active'));

          // 添加当前选项卡和对应面板的活动状态
          this.classList.add('active');
          const tabId = this.getAttribute('data-tab');
          document.getElementById(tabId).classList.add('active');
        });
      });
    });
  </script>
</body>
</html>