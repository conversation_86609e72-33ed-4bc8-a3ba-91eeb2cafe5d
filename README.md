# MeetingGPT - 实时语音检测与处理应用

MeetingGPT是一个基于Electron的跨平台桌面应用，用于实时检测人声和静音，并将音频数据发送到后端进行处理，如翻译或AI问答。

## 功能特点

- 实时人声检测和静音检测
- 在用户说完一句话后，自动检测静音并将音频数据发送到后端
- 后端处理音频数据，返回翻译或AI问答结果
- 前端展示处理结果
- 支持Windows和macOS平台

## 技术栈

- **桌面应用**：Electron + Node.js
- **音频采集**：Web Audio API
- **后端通信**：WebSocket / Socket.IO
- **用户界面**：HTML, CSS, JavaScript

## 安装

```bash
# 克隆仓库
git clone https://github.com/yourusername/meetinggpt.git
cd meetinggpt

# 安装依赖
npm install
```

## 开发运行

```bash
# 启动开发模式
npm start
```

## 构建应用

```bash
# 构建所有平台
npm run build

# 仅构建Windows版本
npm run build:win

# 仅构建macOS版本
npm run build:mac
```

## 配置

应用中的语音检测参数可通过用户界面进行调整：

- **静音阈值**：调整检测语音和静音的灵敏度（0-1之间）
- **静音检测持续时间**：设置多长时间的静音后认为用户说话结束（毫秒）

## 后端服务

该项目需要配合后端服务使用，后端服务负责处理音频数据并返回结果。

## 许可证

ISC 