{"app": {"title": "MeetingGPT - Real-time Voice Detection and Processing", "name": "MeetingGPT"}, "navigation": {"home": "Home", "settings": "Settings", "subscription": "Subscription", "userGuide": "User Guide", "login": "Login/Register", "logout": "Logout"}, "main": {"startRecording": "Start Recording", "stopRecording": "Stop Recording", "clearChat": "Clear Chat History", "newChat": "New Conversation", "status": {"ready": "Ready", "recording": "Recording", "processing": "Processing", "idle": "Idle"}, "transcription": {"title": "Speech Recognition Results", "placeholder": "Speech recognition results will appear here..."}, "aiResponse": {"title": "AI Response", "placeholder": "AI responses will appear here..."}, "messages": {"chatCleared": "All chat history has been cleared.", "newSessionStarted": "New recording session started, previous records cleared.", "newConversationStarted": "New conversation started, ready to begin recording.", "loadingHistory": "Loading chat history...", "historyLoaded": "Loaded {{count}} chat records", "noHistory": "No chat history found", "historyLoadError": "Failed to load chat history"}}, "settings": {"title": "Settings", "audio": {"title": "Audio Settings", "microphone": "Microphone Device", "silenceThreshold": "<PERSON> (0-1)", "silenceDuration": "Silence Detection Duration (ms)", "loading": "Loading...", "device": "Device {{number}}", "microphoneError": "Cannot access microphone", "systemAudioTitle": "How to capture system audio:", "step1": "Make sure Blackhole or similar virtual audio device is installed", "step2": "In system sound settings, set Black<PERSON> as output device", "step3": "Or create multi-output device (Blackhole + Speakers)", "step4": "Select Blackhole as input source in the dropdown menu"}, "ai": {"title": "AI Assistant Settings", "customPromptManagement": "Assistant Management", "addNewPrompt": "Add New Assistant", "promptName": "Assistant Name", "promptNamePlaceholder": "e.g., Translation Assistant", "promptContent": "Assistant Instructions", "promptContentPlaceholder": "Enter assistant instructions, e.g., You are a professional translator, please translate the content I provide...", "noCustomPrompts": "No assistants, please add one", "deleteConfirm": "Are you sure you want to delete this assistant?", "nameRequired": "Please enter assistant name", "contentRequired": "Please enter assistant instructions", "presets": {"translator": {"name": "Translation Assistant", "prompt": "You are a professional translator. Please translate the content I provide accurately and naturally, maintaining the original meaning and tone. If the content is in Chinese, translate it to English; if it's in English, translate it to Chinese."}, "interviewer": {"name": "Interview Assistant", "prompt": "You are a professional interview assistant. Please help analyze the interview content I provide, including key questions asked, candidate responses, strengths and weaknesses observed, and provide hiring recommendations."}}}, "language": {"title": "Language Settings", "current": "Current Language", "select": "Select Language", "changeNote": "Language changes will take effect immediately."}, "subscription": {"title": "Subscription Info", "plan": "Current Plan", "usage": "Usage", "audioTime": "Audio Time", "minutes": "minutes", "upgrade": "Upgrade Plan", "audioQuota": "Audio Transcription Quota", "used": "Used", "manage": "Manage Subscription", "nextBilling": "Next Billing", "loadFailed": "Load Failed", "cannotGet": "Cannot Get", "refreshing": "Refreshing...", "freeVersion": "Free Version", "pleaseLogin": "Please login to view", "loading": "Loading subscription information..."}, "save": "Save Settings", "saved": "Settings Saved"}, "subscription": {"title": "Subscription Service", "redirecting": "Redirecting to subscription page, please wait...", "plans": {"free": "Free Plan", "basic": "Basic Plan", "pro": "Pro Plan", "current": "Current Plan", "monthly": "Monthly Plan", "yearly": "Yearly Plan", "subscription": "Subscription Plan"}}, "login": {"title": "<PERSON><PERSON>", "welcome": "Welcome to MeetingGPT", "description": "Click the button below to login via web", "goToLogin": "Go to Login", "redirecting": "Redirecting to web login...", "features": {"realtime": "Real-time voice recognition and processing", "ai": "Multi-scenario AI assistant support", "summary": "Automatic meeting content organization and summary", "sync": "Multi-device support and data synchronization"}}, "user": {"profile": "Profile Settings", "history": "History", "quota": "<PERSON><PERSON><PERSON>"}, "footer": {"about": "About Us", "terms": "Terms of Service", "privacy": "Privacy Policy", "faq": "FAQ", "contact": "Contact Us", "copyright": "© 2025 MeetingGPT. All rights reserved.", "email": "Contact Email", "backToHome": "Back to Home"}, "messages": {"error": "Error", "success": "Success", "info": "Info", "warning": "Warning", "loading": "Loading...", "noData": "No Data", "networkError": "Network error, please check your connection", "serverError": "Server error, please try again later"}, "common": {"confirm": "Confirm", "cancel": "Cancel", "ok": "OK", "yes": "Yes", "no": "No", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "refresh": "Refresh"}, "history": {"title": "Chat History", "pageTitle": "Chat History - MeetingGPT", "loading": "Loading chat sessions...", "empty": {"title": "No Chat History", "description": "Start a conversation to see your chat history here."}, "session": "Session", "messages": "messages", "noPreview": "No preview available", "yesterday": "Yesterday", "daysAgo": "days ago", "openError": "Failed to open session", "deleteConfirm": "Are you sure you want to delete this session?", "deleteError": "Failed to delete session"}}