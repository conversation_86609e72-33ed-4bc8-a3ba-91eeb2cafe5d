/**
 * 国际化管理器
 * 支持中文和英文双语切换，默认使用系统语言
 */

class I18nManager {
  constructor() {
    this.currentLanguage = 'zh-CN';
    this.translations = {};
    this.supportedLanguages = {
      'zh-CN': '中文',
      'en-US': 'English'
    };
    this.initialized = false;
  }

  /**
   * 初始化国际化管理器
   */
  async init() {
    if (this.initialized) {
      return;
    }

    // 检测系统语言
    this.detectSystemLanguage();

    // 从本地存储加载用户选择的语言
    await this.loadSavedLanguage();

    // 加载语言文件
    await this.loadTranslations();

    this.initialized = true;
  }

  /**
   * 检测系统语言
   */
  detectSystemLanguage() {
    const systemLang = navigator.language || navigator.userLanguage || 'en-US';

    // 检查是否支持系统语言
    if (this.supportedLanguages[systemLang]) {
      this.currentLanguage = systemLang;
    } else if (systemLang.startsWith('zh')) {
      this.currentLanguage = 'zh-CN';
    } else {
      this.currentLanguage = 'en-US';
    }
  }

  /**
   * 从本地存储加载用户选择的语言
   */
  async loadSavedLanguage() {
    try {
      if (typeof require !== 'undefined') {
        // 在Electron环境中使用IPC
        const { ipcRenderer } = require('electron');
        const result = await ipcRenderer.invoke('get-setting', 'language');
        if (result && this.supportedLanguages[result]) {
          this.currentLanguage = result;
        }
      } else {
        // 在浏览器环境中使用localStorage
        const savedLang = localStorage.getItem('meetinggpt-language');
        if (savedLang && this.supportedLanguages[savedLang]) {
          this.currentLanguage = savedLang;
        }
      }
    } catch (error) {
      console.warn('Failed to load saved language:', error);
    }
  }

  /**
   * 保存语言选择到本地存储
   */
  async saveLanguage(language) {
    try {
      if (typeof require !== 'undefined') {
        // 在Electron环境中使用IPC
        const { ipcRenderer } = require('electron');
        await ipcRenderer.invoke('save-setting', 'language', language);
      } else {
        // 在浏览器环境中使用localStorage
        localStorage.setItem('meetinggpt-language', language);
      }
    } catch (error) {
      console.warn('Failed to save language:', error);
    }
  }

  /**
   * 加载翻译文件
   */
  async loadTranslations() {
    try {
      for (const lang of Object.keys(this.supportedLanguages)) {
        const response = await fetch(`./locales/${lang}.json`);
        if (response.ok) {
          this.translations[lang] = await response.json();
        }
      }
    } catch (error) {
      console.error('Failed to load translations:', error);
      // 如果加载失败，使用默认的英文翻译
      this.translations['en-US'] = this.getDefaultTranslations();
    }
  }

  /**
   * 获取默认翻译（英文）
   */
  getDefaultTranslations() {
    return {
      app: { title: "MeetingGPT", name: "MeetingGPT" },
      navigation: { home: "Home", settings: "Settings", subscription: "Subscription" },
      main: { startRecording: "Start Recording", stopRecording: "Stop Recording" },
      common: { save: "Save", cancel: "Cancel", ok: "OK" }
    };
  }

  /**
   * 获取翻译文本
   * @param {string} key - 翻译键，支持点号分隔的嵌套键
   * @param {object} params - 参数对象，用于替换翻译文本中的占位符
   * @returns {string} 翻译后的文本
   */
  t(key, params = {}) {
    const translation = this.getNestedValue(this.translations[this.currentLanguage], key) ||
                       this.getNestedValue(this.translations['en-US'], key) ||
                       key;

    // 替换参数
    return this.replaceParams(translation, params);
  }

  /**
   * 获取嵌套对象的值
   * @param {object} obj - 对象
   * @param {string} path - 路径，用点号分隔
   * @returns {any} 值
   */
  getNestedValue(obj, path) {
    if (!obj) return null;

    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  }

  /**
   * 替换翻译文本中的参数
   * @param {string} text - 原始文本
   * @param {object} params - 参数对象
   * @returns {string} 替换后的文本
   */
  replaceParams(text, params) {
    if (typeof text !== 'string') return text;

    // 支持 {{key}} 格式的参数替换
    return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return params[key] !== undefined ? params[key] : match;
    });
  }

  /**
   * 切换语言
   * @param {string} language - 语言代码
   */
  async switchLanguage(language) {
    if (!this.supportedLanguages[language]) {
      console.warn(`Unsupported language: ${language}`);
      return;
    }

    this.currentLanguage = language;
    await this.saveLanguage(language);

    // 触发语言切换事件
    this.onLanguageChange();
  }

  /**
   * 获取当前语言
   * @returns {string} 当前语言代码
   */
  getCurrentLanguage() {
    return this.currentLanguage;
  }

  /**
   * 获取支持的语言列表
   * @returns {object} 支持的语言对象
   */
  getSupportedLanguages() {
    return this.supportedLanguages;
  }

  /**
   * 语言切换回调
   */
  onLanguageChange() {
    // 更新页面内容
    this.updatePageContent();

    // 触发自定义事件
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('languageChanged', {
        detail: { language: this.currentLanguage }
      }));
    }
  }

  /**
   * 更新页面内容
   */
  updatePageContent() {
    // 检查翻译是否已加载
    if (!this.translations[this.currentLanguage]) {
      console.error('Translations not loaded for language:', this.currentLanguage);
      return;
    }

    // 更新所有带有 data-i18n 属性的元素
    const elements = document.querySelectorAll('[data-i18n]');

    elements.forEach(element => {
      const key = element.getAttribute('data-i18n');
      const translation = this.t(key);

      // 处理不同类型的元素
      if (element.tagName === 'INPUT') {
        if (element.type === 'text' || element.type === 'email' || element.type === 'number') {
          element.placeholder = translation;
        } else {
          element.value = translation;
        }
      } else if (element.tagName === 'TEXTAREA') {
        element.placeholder = translation;
      } else if (element.tagName === 'OPTION') {
        element.textContent = translation;
      } else if (element.tagName === 'SELECT') {
        // 对于select元素，更新其选项
        const options = element.querySelectorAll('option[data-i18n]');
        options.forEach(option => {
          const optionKey = option.getAttribute('data-i18n');
          option.textContent = this.t(optionKey);
        });
      } else {
        element.textContent = translation;
      }
    });

    // 更新页面标题
    const titleKey = document.documentElement.getAttribute('data-i18n-title');
    if (titleKey) {
      document.title = this.t(titleKey);
    }
  }
}

// 创建全局实例
const i18n = new I18nManager();

// 导出实例
// 在Electron环境中，我们需要同时支持module.exports和window全局变量
if (typeof module !== 'undefined' && module.exports) {
  module.exports = i18n;
}

// 总是将i18n附加到window对象（如果存在）
if (typeof window !== 'undefined') {
  window.i18n = i18n;
}
