let ipcRendererInstance;
let uiUtilsInstance;
let settingsManagerInstance; // If needed for specific IPC handlers
let initializeAndRestoreStateFunc;

// DOM elements that might be needed by handlers
let transcriptionContainerElement;
let responseContainerElement;
let statusElement;

// Message history array reference
let messageHistoryRef;

// Stream-related state
let currentStreamMessageDiv = null;
let currentStreamMessageContent = '';
let currentTranscription = null;


const IpcHandlers = {
    init: (ipc, uiUtils, settingsManager, initFunc, msgHistory, domElements) => {
        ipcRendererInstance = ipc;
        uiUtilsInstance = uiUtils;
        settingsManagerInstance = settingsManager; // Store if needed
        initializeAndRestoreStateFunc = initFunc;
        messageHistoryRef = msgHistory;

        transcriptionContainerElement = domElements.transcriptionContainer;
        responseContainerElement = domElements.responseContainer;
        statusElement = domElements.status;


        // Register all IPC event listeners
        ipcRendererInstance.on('audio-response', IpcHandlers._handleAudioResponse);
        ipcRendererInstance.on('audio-response-chunk', IpcHandlers._handleAudioResponseChunk);
        ipcRendererInstance.on('audio-transcription', IpcHandlers._handleAudioTranscription);
        ipcRendererInstance.on('page-loaded', IpcHandlers._handlePageLoaded);

        console.log("IpcHandlers initialized and listeners registered.");
    },

    _handleAudioResponse: async (event, data) => {
        console.log('IPC: Received audio-response:', data);
        if (data.status === 'success') {
            const aiResponse = data.message || '';
            if (currentStreamMessageDiv) {
                console.log('IPC: Stream already handled, skipping duplicate UI update for audio-response.');
                return;
            }

            let userInput = '';
            if (data.transcription) {
                userInput = data.transcription;
            } else if (data.rawResponse) {
                try {
                    if (data.rawResponse.input) userInput = data.rawResponse.input;
                    else if (data.rawResponse.transcript) userInput = data.rawResponse.transcript;
                    else if (data.rawResponse.audioTranscript) userInput = data.rawResponse.audioTranscript;
                    // Simplified extraction, original had more specific cases
                } catch (err) {
                    console.error('IPC: Error extracting transcription from rawResponse:', err);
                }
            }

            if (userInput) {
                const timestamp = new Date();
                uiUtilsInstance.addMessage(transcriptionContainerElement, userInput, true, timestamp);
                messageHistoryRef.push({ role: 'user', content: userInput, timestamp });
            }
            if (aiResponse) {
                const responseTimestamp = new Date();
                uiUtilsInstance.addMessage(responseContainerElement, aiResponse, false, responseTimestamp);
                messageHistoryRef.push({ role: 'assistant', content: aiResponse, timestamp: responseTimestamp });
            }
        } else {
            const errorMessage = `错误: ${data.message}`;
            uiUtilsInstance.addMessage(responseContainerElement, errorMessage, false);
            console.error('IPC: Error in audio-response:', errorMessage);
        }
        if (statusElement) statusElement.textContent = '监听中...';
    },

    _handleAudioResponseChunk: (event, data) => {
        console.log('IPC: Received audio-response-chunk:', data);
        if (data.status === 'chunk') {
            if (!currentStreamMessageDiv) {
                if (data.transcription && !currentTranscription) { // Handle initial transcription if present and not yet shown
                    currentTranscription = data.transcription;
                    const isDuplicateUser = messageHistoryRef.some(msg => msg.role === 'user' && msg.content === data.transcription);
                    if (!isDuplicateUser) {
                        const timestamp = new Date();
                        uiUtilsInstance.addMessage(transcriptionContainerElement, data.transcription, true, timestamp);
                        messageHistoryRef.push({ role: 'user', content: data.transcription, timestamp });
                    }
                }
                const responseTimestamp = new Date();
                currentStreamMessageDiv = uiUtilsInstance.addMessage(responseContainerElement, '', false, responseTimestamp);
                currentStreamMessageContent = '';
            }
            currentStreamMessageContent += data.content;
            const textElement = currentStreamMessageDiv.querySelector('div:first-child');
            if (textElement) {
                try {
                    textElement.innerHTML = uiUtilsInstance.markedParse(currentStreamMessageContent); // Assuming markedParse is part of uiUtils or accessible
                     const codeBlocks = textElement.querySelectorAll('pre code');
                    if (codeBlocks.length > 0) {
                        uiUtilsInstance._ensureHighlightStylesAdded(); // Call the method on the instance
                    }
                } catch (error) {
                    console.error('Markdown parsing error in chunk:', error);
                    textElement.textContent = currentStreamMessageContent; // Fallback
                }
            }
            if (responseContainerElement) responseContainerElement.scrollTop = responseContainerElement.scrollHeight;

        } else if (data.status === 'done') {
            if (currentStreamMessageDiv && currentStreamMessageContent) {
                const isDuplicateAssistant = messageHistoryRef.some(msg => msg.role === 'assistant' && msg.content === currentStreamMessageContent);
                if (!isDuplicateAssistant) {
                    messageHistoryRef.push({ role: 'assistant', content: currentStreamMessageContent, timestamp: new Date() });
                }
            }
            currentStreamMessageDiv = null;
            currentStreamMessageContent = '';
            currentTranscription = null; // Reset transcription tracking
            if (statusElement) statusElement.textContent = '监听中...';

        } else if (data.status === 'error') {
            console.error('IPC: Error in audio-response-chunk:', data.error);
            if (!data.isDuplicate) { // Only show error if not a duplicate request error
                const errorMessage = `错误: ${data.error}`;
                uiUtilsInstance.addMessage(responseContainerElement, errorMessage, false);
            }
            currentStreamMessageDiv = null;
            currentStreamMessageContent = '';
            currentTranscription = null;
            if (statusElement) statusElement.textContent = '监听中...';
        }
    },

    _handleAudioTranscription: (event, data) => {
        console.log('IPC: Received audio-transcription:', data);
        if (data.status === 'success' && data.transcription) {
            const isDuplicate = messageHistoryRef.some(msg => msg.role === 'user' && msg.content === data.transcription);
            if (!isDuplicate) {
                const timestamp = new Date();
                uiUtilsInstance.addMessage(transcriptionContainerElement, data.transcription, true, timestamp);
                messageHistoryRef.push({ role: 'user', content: data.transcription, timestamp });
                currentTranscription = data.transcription; // Keep track to avoid duplication by chunk handler
            } else {
                console.log('IPC: Duplicate user transcription, skipped.');
            }
        }
    },

    _handlePageLoaded: async () => {
        console.log('IPC: Received page-loaded. Restoring state...');
        // Re-initialize SettingsManager if it depends on fresh DOM elements not available at initial init
        // This might be redundant if SettingsManager.init is robust or called appropriately elsewhere.
        // For now, assume renderer.js handles re-init of SettingsManager if needed.
        if (initializeAndRestoreStateFunc) {
            await initializeAndRestoreStateFunc();
        }
        console.log('IPC: Page state restored after page-loaded event.');
    }
};

module.exports = IpcHandlers; 