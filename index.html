<!DOCTYPE html>
<html data-i18n-title="app.title">
<head>
  <meta charset="UTF-8">
  <title>MeetingGPT - Real-time Voice Detection and Processing</title>
  <style>
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f7fa;
      color: #2c3e50;
    }

    /* Markdown样式支持 */
    .message h1, .message h2, .message h3, .message h4, .message h5, .message h6 {
      margin-top: 1.5em;
      margin-bottom: 0.8em;
      font-weight: 600;
      line-height: 1.25;
      color: #333;
    }

    .message h1 { font-size: 1.8em; border-bottom: 1px solid #eaecef; padding-bottom: 0.3em; }
    .message h2 { font-size: 1.5em; border-bottom: 1px solid #eaecef; padding-bottom: 0.3em; }
    .message h3 { font-size: 1.3em; }
    .message h4 { font-size: 1.1em; }

    .message p {
      margin-top: 0;
      margin-bottom: 16px;
      line-height: 1.6;
    }

    .message pre {
      background-color: #f6f8fa;
      border-radius: 6px;
      padding: 16px;
      overflow: auto;
      margin-bottom: 16px;
    }

    .message code {
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
      font-size: 85%;
      background-color: rgba(27, 31, 35, 0.05);
      border-radius: 3px;
      padding: 0.2em 0.4em;
    }

    .message pre code {
      background-color: transparent;
      padding: 0;
      font-size: 90%;
      line-height: 1.45;
    }

    .message ul, .message ol {
      padding-left: 2em;
      margin-top: 0;
      margin-bottom: 16px;
    }

    .message li {
      margin-bottom: 0.25em;
    }

    .message blockquote {
      margin: 0 0 16px 0;
      padding: 0 1em;
      color: #6a737d;
      border-left: 0.25em solid #dfe2e5;
    }

    .message blockquote > :first-child {
      margin-top: 0;
    }

    .message blockquote > :last-child {
      margin-bottom: 0;
    }

    .message table {
      border-collapse: collapse;
      width: 100%;
      margin: 12px 0;
      display: block;
      overflow-x: auto;
    }

    .message table th {
      font-weight: 600;
      background-color: #f6f8fa;
    }

    .message table th, .message table td {
      padding: 6px 13px;
      border: 1px solid #dfe2e5;
    }

    .message table tr {
      background-color: #fff;
      border-top: 1px solid #c6cbd1;
    }

    .message table tr:nth-child(2n) {
      background-color: #f6f8fa;
    }

    .message a {
      color: #0366d6;
      text-decoration: none;
    }

    .message a:hover {
      text-decoration: underline;
    }

    .message img {
      max-width: 100%;
      border-style: none;
      box-sizing: content-box;
    }

    .message hr {
      height: 0.25em;
      padding: 0;
      margin: 24px 0;
      background-color: #e1e4e8;
      border: 0;
    }

    /* 系统消息样式 */
    .system-message {
      background-color: #f1f8ff;
      color: #0366d6;
      padding: 8px 12px;
      border-radius: 4px;
      margin-bottom: 10px;
      transition: opacity 0.5s;
      opacity: 1;
    }

    .header {
      background-color: #1a73e8;
      color: white;
      padding: 0;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .navbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 10px 20px;
    }

    .logo {
      display: flex;
      align-items: center;
    }

    .logo h1 {
      margin: 0;
      font-size: 1.6em;
      font-weight: 500;
    }

    .navigation {
      display: flex;
      align-items: center;
    }

    .nav-link {
      color: white;
      text-decoration: none;
      margin: 0 15px;
      padding: 5px 0;
      position: relative;
      font-weight: 500;
    }

    .nav-link::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0;
      height: 2px;
      background-color: white;
      transition: width 0.3s;
    }

    .nav-link:hover::after {
      width: 100%;
    }

    .user-profile {
      display: flex;
      align-items: center;
      margin-left: 20px;
      position: relative;
      padding: 5px 10px;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      transition: background-color 0.3s;
    }

    .user-profile:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .user-profile .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #fff;
      color: #1a73e8;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: bold;
      font-size: 14px;
      cursor: pointer;
    }

    .user-profile .dropdown {
      position: absolute;
      top: 100%;
      right: 0;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      width: 180px;
      z-index: 1000;
      display: none;
      margin-top: 10px;
    }

    .user-profile .dropdown.active {
      display: block;
    }

    .user-profile .dropdown-item {
      padding: 10px 15px;
      color: #333;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .user-profile .dropdown-item:hover {
      background-color: #f5f7fa;
    }

    .user-profile .dropdown-item.logout {
      border-top: 1px solid #eee;
      color: #e74c3c;
      font-weight: bold;
    }

    .user-profile .dropdown-item.logout:hover {
      background-color: #ffeeee;
    }

    .user-email {
      margin-right: 10px;
      font-size: 14px;
      max-width: 150px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .login-button {
      background-color: white;
      color: #1a73e8;
      padding: 8px 16px;
      border-radius: 20px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s;
      z-index: 100;
      position: relative;
      display: inline-block;
      margin-left: 20px;
      text-decoration: none;
    }

    .login-button:hover {
      background-color: #f0f7ff;
      transform: translateY(-2px);
      box-shadow: 0 2px 10px rgba(26, 115, 232, 0.3);
    }

    .main-container {
      display: flex;
      height: calc(100vh - 250px);
      max-width: 1200px;
      margin: 20px auto;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      background-color: white;
    }

    .panel {
      padding: 20px;
      overflow-y: auto;
    }

    .left-panel {
      flex: 1;
      border-right: 1px solid #e1e4e8;
      background-color: #f8f9fa;
    }

    .right-panel {
      flex: 1;
      background-color: white;
    }

    .controls {
      display: flex;
      justify-content: center;
      background-color: white;
      padding: 15px;
      border-radius: 10px;
      margin: 0 auto 15px;
      max-width: 1200px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    }

    .control-buttons {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    button {
      background-color: #1a73e8;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin: 0 10px;
      transition: all 0.3s;
      display: flex;
      align-items: center;
    }

    button:hover {
      background-color: #0d64d6;
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
    }

    button:disabled {
      background-color: #bdc3c7;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    button .icon {
      margin-right: 8px;
      font-size: 18px;
    }

    .status-container {
      display: flex;
      align-items: center;
      margin-left: 15px;
    }

    .status {
      display: inline-block;
      padding: 5px 10px;
      border-radius: 15px;
      font-size: 14px;
      font-weight: bold;
    }

    .status.recording {
      background-color: #ffebee;
      color: #e53935;
    }

    .status.idle {
      background-color: #e8f5e9;
      color: #43a047;
    }

    .visualizer-container {
      width: 100%;
      height: 70px;
      background-color: #2c3e50;
      margin: 15px 0;
      border-radius: 8px;
      overflow: hidden;
    }

    #visualizer {
      width: 100%;
      height: 100%;
    }

    .settings-container {
      max-width: 1200px;
      margin: 20px auto;
      background-color: white;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    }

    .settings {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }

    .setting-group {
      flex: 1;
      min-width: 250px;
      margin: 10px;
    }

    .settings h2 {
      color: #1a73e8;
      font-size: 1.3em;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e1e4e8;
      display: flex;
      align-items: center;
    }

    .settings h2 .icon {
      margin-right: 8px;
      font-size: 20px;
    }

    .settings label {
      display: block;
      margin: 15px 0 5px;
      color: #4a5568;
      font-weight: 500;
    }

    select, input, textarea {
      width: 100%;
      padding: 10px;
      border-radius: 4px;
      border: 1px solid #ddd;
      background-color: #f8f9fa;
      font-size: 15px;
      transition: border 0.3s;
    }

    select:focus, input:focus, textarea:focus {
      border-color: #1a73e8;
      outline: none;
      box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
    }

    textarea {
      min-height: 100px;
      resize: vertical;
    }

    .message {
      padding: 12px 15px;
      margin: 10px 0;
      border-radius: 8px;
      line-height: 1.5;
      position: relative;
    }

    .user-message {
      background-color: #f0f7ff;
      border-left: 4px solid #1a73e8;
      font-size: 0.85em; /* 语音识别结果使用较小字体 */
      padding: 10px 12px; /* 减少内边距以节省空间 */
      margin: 8px 0; /* 减少外边距 */
    }

    .ai-message {
      background-color: #f1f8e9;
      border-left: 4px solid #7cb342;
      font-size: 0.85em; /* AI响应也使用较小字体，与语音识别结果保持一致 */
      padding: 10px 12px; /* 减少内边距以节省空间 */
      margin: 8px 0; /* 减少外边距 */
    }

    .timestamp {
      font-size: 11px;
      color: #999;
      margin-top: 5px;
      text-align: right;
    }

    /* 为语音识别结果和AI响应中的时间戳提供更小的字体 */
    .user-message .timestamp,
    .ai-message .timestamp {
      font-size: 10px;
    }

    .panel-title {
      margin-top: 0;
      color: #1a73e8;
      border-bottom: 2px solid #e1e4e8;
      padding-bottom: 10px;
      margin-bottom: 20px;
      font-size: 1.2em;
      display: flex;
      align-items: center;
    }

    .panel-title .icon {
      margin-right: 8px;
      font-size: 18px;
    }

    .messages-container {
      max-height: calc(100% - 40px);
      overflow-y: auto;
    }

    .footer {
      text-align: center;
      padding: 20px 0;
      color: #718096;
      font-size: 0.9em;
      background-color: white;
      margin-top: 20px;
      border-top: 1px solid #e1e4e8;
    }

    .footer-links {
      display: flex;
      justify-content: center;
      margin-bottom: 10px;
    }

    .footer-link {
      color: #1a73e8;
      margin: 0 15px;
      text-decoration: none;
    }

    .footer-link:hover {
      text-decoration: underline;
    }

    .subscription-info {
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #e8f5e9;
      border-radius: 8px;
      padding: 10px;
      margin-top: 15px;
      color: #43a047;
      font-weight: 500;
    }

    .upgrade-link {
      margin-left: 10px;
      color: #1a73e8;
      font-weight: bold;
      text-decoration: none;
    }

    .upgrade-link:hover {
      text-decoration: underline;
    }

    /* 确保用户信息和登录按钮容器与导航栏对齐 */
    #userProfileContainer, #loginButtonContainer {
      display: flex;
      align-items: center;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="navbar">
      <div class="logo">
        <h1 data-i18n="app.name">MeetingGPT</h1>
      </div>
      <div class="navigation">
        <a href="index.html" class="nav-link" data-i18n="navigation.home">Home</a>
        <a href="settings.html" class="nav-link" data-i18n="navigation.settings">Settings</a>
        <a href="subscription.html" class="nav-link" data-i18n="navigation.subscription">Subscription</a>
        <a class="nav-link" id="userGuideLink" data-i18n="navigation.userGuide">User Guide</a>

        <!-- 用户信息区域（登录后显示） -->
        <div id="userProfileContainer" style="display: none;">
          <div class="user-profile">
            <span class="user-email" id="userEmail"></span>
            <div class="avatar" id="userAvatar"></div>
            <div class="dropdown" id="userDropdown">
              <div class="dropdown-item" data-i18n="user.history">History</div>
              <div class="dropdown-item logout" id="logoutButton" data-i18n="navigation.logout">Logout</div>
            </div>
          </div>
        </div>

        <!-- 登录按钮（未登录时显示） -->
        <div id="loginButtonContainer">
          <a href="login.html" class="login-button" data-i18n="navigation.login">Login/Register</a>
        </div>
      </div>
    </div>
  </div>

  <div class="controls">
    <div class="control-buttons">
      <button id="startBtn">
        <span class="icon">▶</span><span data-i18n="main.startRecording">Start Recording</span>
      </button>
      <button id="stopBtn" disabled>
        <span class="icon">■</span><span data-i18n="main.stopRecording">Stop Recording</span>
      </button>
      <button id="newChatBtn" style="background-color: #27ae60;">
        <span class="icon">💬</span><span data-i18n="main.newChat">New Conversation</span>
      </button>
    </div>
    <div class="status-container">
      <div id="status" class="status idle" data-i18n="main.status.ready">Ready</div>
    </div>
  </div>

  <div class="visualizer-container" style="max-width: 1200px; margin: 20px auto; border-radius: 10px; overflow: hidden;">
    <canvas id="visualizer"></canvas>
  </div>

  <!-- 添加隐藏的设置元素，供JavaScript使用 -->
  <div style="display: none;">
    <select id="audioDevices"></select>
    <input type="range" id="silenceThreshold" min="0" max="1" step="0.01" value="0.05">
    <input type="number" id="silenceDuration" min="500" max="5000" step="100" value="1500">
    <select id="promptType">
      <option value="interview" data-i18n="settings.ai.types.interview">Interview Assistant</option>
      <option value="translator" data-i18n="settings.ai.types.translator">Translator</option>
      <option value="coder" data-i18n="settings.ai.types.coder">Coding Assistant</option>
      <option value="custom" data-i18n="settings.ai.types.custom">Custom</option>
    </select>
    <div id="customPromptContainer">
      <textarea id="customPrompt"></textarea>
    </div>
    <div class="settings-panel"></div>
  </div>

  <div class="main-container">
    <div class="panel left-panel">
      <h3 class="panel-title"><span class="icon">🔊</span><span data-i18n="main.transcription.title">Speech Recognition Results</span></h3>
      <div id="transcription-container" class="messages-container">
        <!-- 语音识别结果将显示在这里 -->
      </div>
    </div>

    <div class="panel right-panel">
      <h3 class="panel-title"><span class="icon">💬</span><span data-i18n="main.aiResponse.title">AI Response</span></h3>
      <div id="response-container" class="messages-container">

      </div>
    </div>
  </div>

  <div class="footer">
    <div class="footer-links">
      <a href="#" class="footer-link" data-i18n="footer.about">About Us</a>
      <a href="#" class="footer-link" data-i18n="footer.terms">Terms of Service</a>
      <a href="#" class="footer-link" data-i18n="footer.privacy">Privacy Policy</a>
      <a class="footer-link" id="footerUserGuideLink" data-i18n="navigation.userGuide">User Guide</a>
      <a href="#" class="footer-link" data-i18n="footer.faq">FAQ</a>
      <a href="mailto:<EMAIL>" class="footer-link" data-i18n="footer.contact">Contact Us</a>
    </div>
    <p><span data-i18n="footer.copyright">© 2025 MeetingGPT. All rights reserved.</span> | <span data-i18n="footer.email">Contact Email</span>: <a href="mailto:<EMAIL>" style="color: #1a73e8;"><EMAIL></a></p>
  </div>

  <script src="i18n.js"></script>
  <script src="languageSwitcher.js"></script>
  <script src="renderer.js"></script>
</body>
</html>