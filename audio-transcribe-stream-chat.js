const fs = require('fs');
const apiClient = require('./api');

/**
 * 音频转录并流式聊天示例
 * 展示如何转录音频文件并将结果通过流式聊天API获取响应
 */
async function audioTranscribeStreamChat() {
  try {
    // 步骤1: 使用测试音频或生成一个
    let audioBuffer;
    
    // 检查是否存在test-sine.wav，如果存在则使用，否则生成一个
    if (fs.existsSync('test-sine.wav')) {
      console.log('使用现有音频文件: test-sine.wav');
      audioBuffer = fs.readFileSync('test-sine.wav');
    } else {
      console.log('生成测试音频文件...');
      // 创建一个简单的正弦波
      const duration = 2; // 2秒钟
      const sampleRate = 44100;
      const frequency = 440; // A4音符
      const volume = 0.5;
      
      const samples = [];
      for (let i = 0; i < duration * sampleRate; i++) {
        const t = i / sampleRate;
        // 生成一个简单的正弦波形
        samples.push(Math.sin(2 * Math.PI * frequency * t) * volume);
      }
      
      // 使用我们的函数将样本转换为WAV格式
      audioBuffer = apiClient.createWavFile(samples, {
        sampleRate: sampleRate,
        numChannels: 1
      });
      
      // 保存文件以便将来使用
      fs.writeFileSync('test-sine.wav', audioBuffer);
      console.log('生成的WAV文件已保存为 test-sine.wav');
    }
    
    // 步骤2: 转录音频
    console.log('开始转录音频...');
    const transcription = await apiClient.transcribeAudio(audioBuffer, 'whisper-1', 'verbose_json');
    console.log('转录结果:', transcription);
    
    // 步骤3: 准备聊天提示
    const prompt = '请分析以下音频转录内容，并给出详细回复:';
    const messages = [
      { role: 'system', content: '你是一个有帮助的助手。' },
      { role: 'user', content: prompt + '\n' + transcription }
    ];
    
    console.log('正在发送转录文本到流式聊天API...');
    
    // 完整的回复内容
    let fullResponse = '';
    
    // 设置流式响应的回调函数
    const handleChunk = (chunk) => {
      // 如果收到完成标志
      if (chunk.done) {
        console.log('\n\n--- 流式响应完成 ---');
        console.log('完整响应:\n', fullResponse);
        return;
      }
      
      // 如果发生错误
      if (chunk.error) {
        console.error('流式响应错误:', chunk.error);
        return;
      }
      
      // 提取响应内容
      try {
        if (chunk.choices && chunk.choices[0].delta && chunk.choices[0].delta.content) {
          const content = chunk.choices[0].delta.content;
          // 追加到完整响应
          fullResponse += content;
          // 实时打印每个内容片段
          process.stdout.write(content);
        }
      } catch (error) {
        console.error('处理响应块失败:', error);
      }
    };
    
    // 发送流式聊天请求
    await apiClient.chatCompletion(
      'gpt-3.5-turbo', // 模型
      messages,        // 消息数组
      true,            // 启用流式输出
      handleChunk      // 处理每个数据块的回调函数
    );
    
  } catch (error) {
    console.error('音频转录和流式聊天过程中出错:', error);
  }
}

// 运行示例
audioTranscribeStreamChat(); 