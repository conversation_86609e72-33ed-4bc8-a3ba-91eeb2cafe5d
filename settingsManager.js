// Dependencies to be injected
let ipcRendererInstance;
let audioManagerInstance;
let domElements; // { silenceThresholdInput, silenceDurationInput, promptTypeSelect, customPromptContainer, customPromptTextarea }

// Internal state
let internalState = {
    currentPromptType: 'interview',
    customPrompts: [],
    selectedPromptId: '',
    silenceThreshold: 0.01,
    silenceDuration: 2000,
    silence_counter_threshold: 3,
    selectedAudioDeviceId: '',
};

function _saveAppSettingsToLocalStorage() {
    try {
        const settingsToSave = {
            promptType: internalState.currentPromptType,
            selectedPromptId: internalState.selectedPromptId,
            // customPrompts are saved separately by their event listener for now
        };
        localStorage.setItem('meetingGptSettings', JSON.stringify(settingsToSave));
        console.log('SettingsManager: App settings saved to localStorage.');
    } catch (error) {
        console.error('SettingsManager: 保存设置到localStorage时出错:', error);
    }
}

function _loadAppSettingsFromLocalStorage() {
    try {
        const savedSettings = localStorage.getItem('meetingGptSettings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            console.log('SettingsManager: 加载已保存的应用设置:', settings);
            if (settings.promptType) internalState.currentPromptType = settings.promptType;
            if (settings.selectedPromptId) internalState.selectedPromptId = settings.selectedPromptId;
        }

        const savedPrompts = localStorage.getItem('customPrompts');
        if (savedPrompts) {
            internalState.customPrompts = JSON.parse(savedPrompts);
        } else {
            // Default custom prompt if none are saved
            internalState.customPrompts = [
                {
                  id: Date.now().toString(36) + Math.random().toString(36).substring(2),
                  name: '会议总结助手',
                  prompt: '你是一位专业会议记录员，请根据我的会议内容提供清晰的会议摘要，包括关键讨论点、决策和后续行动。'
                }
            ];
            localStorage.setItem('customPrompts', JSON.stringify(internalState.customPrompts));
        }
        // Ensure selectedPromptId is valid or default to first custom prompt's id
        if (internalState.currentPromptType === 'custom' && internalState.customPrompts.length > 0) {
            const exists = internalState.customPrompts.some(p => p.id === internalState.selectedPromptId);
            if (!exists && internalState.customPrompts[0]) {
                internalState.selectedPromptId = internalState.customPrompts[0].id;
            }
        }
    } catch (error) {
        console.error('SettingsManager: 加载应用设置时出错:', error);
        // Initialize with defaults if error
        if (!internalState.customPrompts || internalState.customPrompts.length === 0) {
            internalState.customPrompts = [{ id: 'default-error-prompt', name: '默认助手', prompt: '你是一位AI助手，请帮助我解析语音内容并给出有用的回应。' }];
            internalState.selectedPromptId = 'default-error-prompt';
        }
    }
}

function _updateAudioManagerConfig() {
    if (audioManagerInstance && audioManagerInstance.isRecording()) {
        console.log('SettingsManager: Re-configuring AudioManager due to settings change.');
        audioManagerInstance.configure({
            silenceThreshold: internalState.silenceThreshold,
            silenceDuration: internalState.silenceDuration,
            // getCurrentPrompt is a method of SettingsManager, so pass it directly if AudioManager expects a function
            // If AudioManager expects the text, call it: SettingsManager.getCurrentPromptText()
            getCurrentPrompt: SettingsManager.getCurrentPromptText // Pass the function that returns the text
        });
    }
}

const SettingsManager = {
    init: (ipc, audioManager, elements) => {
        ipcRendererInstance = ipc;
        audioManagerInstance = audioManager;
        domElements = elements;

        // Set initial values from DOM elements (these might be overwritten by loaded state)
        internalState.silenceThreshold = parseFloat(domElements.silenceThresholdInput.value);
        internalState.silenceDuration = parseInt(domElements.silenceDurationInput.value);
        internalState.currentPromptType = domElements.promptTypeSelect.value;

        // Attach event listeners
        domElements.silenceThresholdInput.addEventListener('change', () => {
            internalState.silenceThreshold = parseFloat(domElements.silenceThresholdInput.value);
            // _saveAppSettingsToLocalStorage(); // This was for app settings, not recording params primarily
            SettingsManager.saveCurrentRecordingConfigToMain();
            _updateAudioManagerConfig();
        });

        domElements.silenceDurationInput.addEventListener('change', () => {
            internalState.silenceDuration = parseInt(domElements.silenceDurationInput.value);
            // _saveAppSettingsToLocalStorage();
            SettingsManager.saveCurrentRecordingConfigToMain();
            _updateAudioManagerConfig();
        });

        domElements.promptTypeSelect.addEventListener('change', () => {
            internalState.currentPromptType = domElements.promptTypeSelect.value;
            domElements.customPromptContainer.style.display = internalState.currentPromptType === 'custom' ? 'block' : 'none';
            _saveAppSettingsToLocalStorage();
            // Prompt type change doesn't immediately reconfigure AudioManager or save to main
            // It will be used next time getCurrentPromptText is called.
        });

        domElements.customPromptTextarea.addEventListener('input', () => {
            const currentPromptId = (internalState.customPrompts.length > 0 && internalState.selectedPromptId)
                                    ? internalState.selectedPromptId
                                    : (internalState.customPrompts[0] ? internalState.customPrompts[0].id : null);

            if (currentPromptId) {
                const promptIndex = internalState.customPrompts.findIndex(p => p.id === currentPromptId);
                if (promptIndex !== -1) {
                    internalState.customPrompts[promptIndex].prompt = domElements.customPromptTextarea.value;
                } else { // Fallback: update first prompt or create new if list was empty/ID mismatch
                    if (internalState.customPrompts.length > 0) {
                       internalState.customPrompts[0].prompt = domElements.customPromptTextarea.value;
                       internalState.selectedPromptId = internalState.customPrompts[0].id;
                    } else {
                         const newPrompt = {
                            id: Date.now().toString(36) + Math.random().toString(36).substring(2),
                            name: '自定义助手', // Default name
                            prompt: domElements.customPromptTextarea.value
                         };
                         internalState.customPrompts.push(newPrompt);
                         internalState.selectedPromptId = newPrompt.id;
                    }
                }
            } else { // No selected/default custom prompt, create one
                 const newPrompt = {
                    id: Date.now().toString(36) + Math.random().toString(36).substring(2),
                    name: '自定义助手',
                    prompt: domElements.customPromptTextarea.value
                 };
                 internalState.customPrompts.push(newPrompt);
                 internalState.selectedPromptId = newPrompt.id;
            }
            localStorage.setItem('customPrompts', JSON.stringify(internalState.customPrompts));
            _saveAppSettingsToLocalStorage(); // Save general app settings which might include selectedPromptId
        });
    },

    loadInitialStateFromMainAndStorage: (stateFromMain) => {
        // Load recording-specific settings from main process state first
        if (stateFromMain) {
            console.log('SettingsManager: Loading state from main process:', stateFromMain);
            internalState.selectedAudioDeviceId = stateFromMain.audioDeviceId || '';
            // Prioritize main process state for these recording parameters
            internalState.silenceThreshold = stateFromMain.silenceThreshold !== undefined ? stateFromMain.silenceThreshold : parseFloat(domElements.silenceThresholdInput.value);
            internalState.silenceDuration = stateFromMain.silenceDuration !== undefined ? stateFromMain.silenceDuration : parseInt(domElements.silenceDurationInput.value);
            internalState.silence_counter_threshold = stateFromMain.silence_counter_threshold !== undefined ? stateFromMain.silence_counter_threshold : 3;
        } else {
            console.log('SettingsManager: No state from main process, using DOM defaults for recording params.');
            internalState.silenceThreshold = parseFloat(domElements.silenceThresholdInput.value);
            internalState.silenceDuration = parseInt(domElements.silenceDurationInput.value);
            internalState.silence_counter_threshold = 3; // Default
        }

        // Then load app-specific settings from localStorage (prompts, etc.)
        _loadAppSettingsFromLocalStorage();

        // Update UI elements based on the fully loaded/merged state
        domElements.silenceThresholdInput.value = internalState.silenceThreshold;
        domElements.silenceDurationInput.value = internalState.silenceDuration;
        domElements.promptTypeSelect.value = internalState.currentPromptType;
        domElements.customPromptContainer.style.display = internalState.currentPromptType === 'custom' ? 'block' : 'none';

        if (internalState.currentPromptType === 'custom') {
            const currentCustomPrompt = internalState.customPrompts.find(p => p.id === internalState.selectedPromptId) || internalState.customPrompts[0];
            if (currentCustomPrompt) {
                 domElements.customPromptTextarea.value = currentCustomPrompt.prompt;
            } else {
                 domElements.customPromptTextarea.value = ''; // Clear if no custom prompt
            }
        }
        // Note: selectedAudioDeviceId is used by renderer.js to set the audioDevicesSelect dropdown value
        console.log('SettingsManager: Initial state loaded and UI updated.', internalState);
    },

    saveCurrentRecordingConfigToMain: () => {
        console.log('SettingsManager: Sending recording config to main process:', {
            audioDeviceId: internalState.selectedAudioDeviceId,
            silenceThreshold: internalState.silenceThreshold,
            silenceDuration: internalState.silenceDuration,
            silence_counter_threshold: internalState.silence_counter_threshold
        });
        if (ipcRendererInstance) {
            ipcRendererInstance.send('save-recording-state', {
                audioDeviceId: internalState.selectedAudioDeviceId,
                silenceThreshold: internalState.silenceThreshold,
                silenceDuration: internalState.silenceDuration,
                silence_counter_threshold: internalState.silence_counter_threshold
            });
        }
    },

    // Getter methods
    getSilenceThreshold: () => internalState.silenceThreshold,
    getSilenceDuration: () => internalState.silenceDuration,
    getSilenceCounterThreshold: () => internalState.silence_counter_threshold,
    getSelectedAudioDeviceId: () => internalState.selectedAudioDeviceId,

    getCurrentPromptText: () => {
        // 🔧 优先从新的助手管理系统获取prompt
        try {
            const savedSettings = localStorage.getItem('meetingGptSettings');
            const savedPrompts = localStorage.getItem('customPrompts');

            if (savedSettings && savedPrompts) {
                const settings = JSON.parse(savedSettings);
                const customPrompts = JSON.parse(savedPrompts);

                if (settings.selectedPromptId && customPrompts) {
                    const selectedPrompt = customPrompts.find(p => p.id === settings.selectedPromptId);
                    if (selectedPrompt && selectedPrompt.prompt) {
                        console.log('🎯 SettingsManager: 使用新助手系统的prompt:', selectedPrompt.name);
                        return selectedPrompt.prompt;
                    }
                }
            }
        } catch (error) {
            console.error('SettingsManager: 读取新助手系统设置失败:', error);
        }

        // 🔧 回退到旧系统逻辑（兼容性）
        if (internalState.currentPromptType === 'custom') {
            const selected = internalState.customPrompts.find(p => p.id === internalState.selectedPromptId);
            if (selected) return selected.prompt;
            if (internalState.customPrompts.length > 0 && internalState.customPrompts[0]) return internalState.customPrompts[0].prompt;
            return '你是一位AI助手，请帮助我解析语音内容并给出有用的回应。'; // Default fallback for custom
        }
        const prompts = {
            'interview': '你是一位经验丰富的面试官，请根据我的回答给出专业的面试反馈和建议。',
            'translator': '你是一位精通多国语言的翻译官，请将我的话准确翻译成英文。',
            'coder': '你是一位专业的程序员，请根据我的描述提供相应的代码或技术建议。'
        };
        return prompts[internalState.currentPromptType] || prompts['interview']; // Default to interview prompt
    },

    // Setter methods
    setSelectedAudioDeviceId: (deviceId) => {
        internalState.selectedAudioDeviceId = deviceId;
        SettingsManager.saveCurrentRecordingConfigToMain(); // Save when device changes
    },
    setSilenceCounterThreshold: (threshold) => { // If this needs to be set from outside
        internalState.silence_counter_threshold = threshold;
        SettingsManager.saveCurrentRecordingConfigToMain();
    }
};

module.exports = SettingsManager;