const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// DOM元素
const visualizer = document.getElementById('visualizer');
const silenceThresholdInput = document.getElementById('silenceThreshold');
const silenceDurationInput = document.getElementById('silenceDuration');
const audioDevicesSelect = document.getElementById('audioDevices');

const customPromptsList = document.getElementById('customPromptsList');
const addCustomPromptBtn = document.getElementById('addCustomPromptBtn');
const customPromptForm = document.getElementById('customPromptForm');
const customPromptName = document.getElementById('customPromptName');
const customPrompt = document.getElementById('customPrompt');
const cancelCustomPromptBtn = document.getElementById('cancelCustomPromptBtn');
const saveCustomPromptBtn = document.getElementById('saveCustomPromptBtn');
const saveSettingsBtn = document.getElementById('saveSettingsBtn');

// 设置变量
let silenceThreshold = parseFloat(silenceThresholdInput.value);
let silenceDuration = parseInt(silenceDurationInput.value);
let selectedAudioDeviceId = '';

let selectedPromptId = '';
let editingPromptId = null;
let customPrompts = [];

// 可视化相关变量
const visualizerCtx = visualizer.getContext('2d');
let canvasWidth, canvasHeight;
let audioContext;
let audioStream;
let analyser;
let isVisualizing = false;

// 初始化可视化
function setupVisualizer() {
  canvasWidth = visualizer.width = visualizer.offsetWidth;
  canvasHeight = visualizer.height = visualizer.offsetHeight;
}

// 加载音频设备列表
async function loadAudioDevices() {
  try {
    // 请求麦克风权限
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    stream.getTracks().forEach(track => track.stop()); // 停止流，我们只需要获取权限

    // 获取设备列表
    const devices = await navigator.mediaDevices.enumerateDevices();

    // 显示所有音频输入设备
    const audioDevices = devices.filter(device => device.kind === 'audioinput');

    // 清空下拉菜单
    audioDevicesSelect.innerHTML = '';

    // 添加设备到下拉菜单
    audioDevices.forEach(device => {
      const option = document.createElement('option');
      option.value = device.deviceId;
      const deviceLabel = device.label || (window.i18n ? window.i18n.t('settings.audio.device', {number: audioDevicesSelect.options.length + 1}) : `Device ${audioDevicesSelect.options.length + 1}`);
      option.text = `Input: ${deviceLabel}`;
      audioDevicesSelect.appendChild(option);
    });

    // 如果有设备，选择第一个，但不覆盖已保存的设备选择
    if (audioDevices.length > 0 && !selectedAudioDeviceId) {
      selectedAudioDeviceId = audioDevices[0].deviceId;
    }

    // 恢复保存的设备选择
    if (selectedAudioDeviceId) {
      for (let i = 0; i < audioDevicesSelect.options.length; i++) {
        if (audioDevicesSelect.options[i].value === selectedAudioDeviceId) {
          audioDevicesSelect.selectedIndex = i;
          break;
        }
      }
    }

    console.log('麦克风权限状态:', 'granted');
    console.log('当前选择的音频设备ID:', selectedAudioDeviceId);

    return true;
  } catch (err) {
    console.error('获取麦克风设备失败:', err);
    const errorMessage = window.i18n ? window.i18n.t('settings.audio.microphoneError') : 'Cannot access microphone';
    audioDevicesSelect.innerHTML = `<option value="">${errorMessage}</option>`;
    console.log('麦克风权限状态:', 'denied');
    return false;
  }
}

// 开始可视化
async function startVisualizing() {
  if (isVisualizing) return;

  try {
    // 创建音频上下文
    audioContext = new (window.AudioContext || window.webkitAudioContext)();

    // 获取麦克风输入
    const constraints = {
      audio: {
        deviceId: selectedAudioDeviceId ? { exact: selectedAudioDeviceId } : undefined,
        echoCancellation: false,
        noiseSuppression: false,
        autoGainControl: false
      }
    };

    audioStream = await navigator.mediaDevices.getUserMedia(constraints);

    // 设置音频处理节点
    const source = audioContext.createMediaStreamSource(audioStream);
    analyser = audioContext.createAnalyser();
    analyser.fftSize = 256;
    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    source.connect(analyser);

    isVisualizing = true;

    // 开始可视化
    function draw() {
      if (!isVisualizing) return;

      requestAnimationFrame(draw);

      analyser.getByteFrequencyData(dataArray);

      visualizerCtx.clearRect(0, 0, canvasWidth, canvasHeight);

      const barWidth = (canvasWidth / dataArray.length) * 2.5;
      let barHeight;
      let x = 0;

      visualizerCtx.fillStyle = '#3498db';

      for (let i = 0; i < dataArray.length; i++) {
        barHeight = dataArray[i] / 255 * canvasHeight;

        visualizerCtx.fillRect(x, canvasHeight - barHeight, barWidth, barHeight);
        x += barWidth + 1;
      }
    }

    draw();

  } catch (err) {
    console.error('启动可视化失败:', err);
    stopVisualizing();
  }
}

// 停止可视化
function stopVisualizing() {
  if (audioStream) {
    audioStream.getTracks().forEach(track => track.stop());
  }

  if (audioContext && audioContext.state !== 'closed') {
    audioContext.close();
  }

  isVisualizing = false;

  // 清除可视化
  visualizerCtx.clearRect(0, 0, canvasWidth, canvasHeight);
}

// 加载自定义指令
function loadCustomPrompts() {
  try {
    const savedPrompts = localStorage.getItem('customPrompts');
    if (savedPrompts) {
      customPrompts = JSON.parse(savedPrompts);

      // 检查是否需要添加预设助手（向后兼容）
      const hasTranslator = customPrompts.some(p => p.isPreset && (p.name === 'Translation Assistant' || p.name === '翻译助手'));
      const hasInterviewer = customPrompts.some(p => p.isPreset && (p.name === 'Interview Assistant' || p.name === '面试助手'));

      if (!hasTranslator) {
        const translatorName = window.i18n ? window.i18n.t('settings.ai.presets.translator.name') : 'Translation Assistant';
        const translatorPrompt = window.i18n ? window.i18n.t('settings.ai.presets.translator.prompt') : 'You are a professional translator. Please translate the content I provide accurately and naturally, maintaining the original meaning and tone. If the content is in Chinese, translate it to English; if it\'s in English, translate it to Chinese.';

        customPrompts.unshift({
          id: generateId(),
          name: translatorName,
          prompt: translatorPrompt,
          isPreset: true
        });
      }

      if (!hasInterviewer) {
        const interviewerName = window.i18n ? window.i18n.t('settings.ai.presets.interviewer.name') : 'Interview Assistant';
        const interviewerPrompt = window.i18n ? window.i18n.t('settings.ai.presets.interviewer.prompt') : 'You are a professional interview assistant. Please help analyze the interview content I provide, including key questions asked, candidate responses, strengths and weaknesses observed, and provide hiring recommendations.';

        customPrompts.splice(1, 0, {
          id: generateId(),
          name: interviewerName,
          prompt: interviewerPrompt,
          isPreset: true
        });
      }

      // 如果添加了预设助手，保存到localStorage
      if (!hasTranslator || !hasInterviewer) {
        saveCustomPrompts();
      }
    } else {
      // 预设两个助手：翻译助手和面试助手
      const translatorName = window.i18n ? window.i18n.t('settings.ai.presets.translator.name') : 'Translation Assistant';
      const translatorPrompt = window.i18n ? window.i18n.t('settings.ai.presets.translator.prompt') : 'You are a professional translator. Please translate the content I provide accurately and naturally, maintaining the original meaning and tone. If the content is in Chinese, translate it to English; if it\'s in English, translate it to Chinese.';

      const interviewerName = window.i18n ? window.i18n.t('settings.ai.presets.interviewer.name') : 'Interview Assistant';
      const interviewerPrompt = window.i18n ? window.i18n.t('settings.ai.presets.interviewer.prompt') : 'You are a professional interview assistant. Please help analyze the interview content I provide, including key questions asked, candidate responses, strengths and weaknesses observed, and provide hiring recommendations.';

      customPrompts = [
        {
          id: generateId(),
          name: translatorName,
          prompt: translatorPrompt,
          isPreset: true
        },
        {
          id: generateId(),
          name: interviewerName,
          prompt: interviewerPrompt,
          isPreset: true
        }
      ];
      saveCustomPrompts();
    }

    // 如果没有选择的助手，默认选择第一个
    if (!selectedPromptId && customPrompts.length > 0) {
      selectedPromptId = customPrompts[0].id;
    }

    renderCustomPrompts();
  } catch (error) {
    console.error('加载自定义指令失败:', error);
    customPrompts = [];
  }
}

// 保存自定义指令到localStorage
function saveCustomPrompts() {
  localStorage.setItem('customPrompts', JSON.stringify(customPrompts));
}

// 渲染自定义指令列表
function renderCustomPrompts() {
  // 清空列表
  customPromptsList.innerHTML = '';

  if (customPrompts.length === 0) {
    const noPromptsMessage = window.i18n ? window.i18n.t('settings.ai.noCustomPrompts') : 'No assistants, please add one';
    customPromptsList.innerHTML = `<div class="custom-prompt-item">${noPromptsMessage}</div>`;
    return;
  }

  // 填充列表
  customPrompts.forEach(promptItem => {
    // 添加到列表
    const item = document.createElement('div');
    item.className = 'custom-prompt-item';
    item.setAttribute('data-id', promptItem.id);

    // 如果是当前选择的助手，添加选中样式
    if (selectedPromptId === promptItem.id) {
      item.classList.add('selected');
    }

    const editText = window.i18n ? window.i18n.t('common.edit') : 'Edit';
    const deleteText = window.i18n ? window.i18n.t('common.delete') : 'Delete';

    // 对于预设助手，使用国际化的名称
    let displayName = promptItem.name;
    if (promptItem.isPreset && window.i18n) {
      if (promptItem.name === 'Translation Assistant' || promptItem.name === '翻译助手') {
        displayName = window.i18n.t('settings.ai.presets.translator.name');
      } else if (promptItem.name === 'Interview Assistant' || promptItem.name === '面试助手') {
        displayName = window.i18n.t('settings.ai.presets.interviewer.name');
      }
    }

    // 预设助手不能删除，只能编辑
    const deleteButton = promptItem.isPreset ? '' : `<button class="custom-prompt-action delete-action" data-id="${promptItem.id}">🗑️ ${deleteText}</button>`;

    item.innerHTML = `
      <div class="custom-prompt-name">${displayName}</div>
      <div class="custom-prompt-actions">
        <button class="custom-prompt-action edit-action" data-id="${promptItem.id}">✏️ ${editText}</button>
        ${deleteButton}
      </div>
    `;

    // 添加点击选择功能
    item.addEventListener('click', (e) => {
      // 如果点击的是按钮，不触发选择
      if (e.target.classList.contains('custom-prompt-action')) {
        return;
      }
      selectPrompt(promptItem.id);
    });

    customPromptsList.appendChild(item);
  });

  // 添加编辑事件监听
  document.querySelectorAll('.edit-action').forEach(button => {
    button.addEventListener('click', () => {
      const promptId = button.getAttribute('data-id');
      editPrompt(promptId);
    });
  });

  // 添加删除事件监听
  document.querySelectorAll('.delete-action').forEach(button => {
    button.addEventListener('click', () => {
      const promptId = button.getAttribute('data-id');
      deletePrompt(promptId);
    });
  });
}

// 选择助手
function selectPrompt(promptId) {
  selectedPromptId = promptId;

  // 移除所有选中状态
  document.querySelectorAll('.custom-prompt-item').forEach(item => {
    item.classList.remove('selected');
  });

  // 添加选中状态到当前项
  const selectedItem = document.querySelector(`[data-id="${promptId}"]`);
  if (selectedItem) {
    selectedItem.classList.add('selected');
  }

  // 保存设置
  saveSettings(false);

  console.log('已选择助手:', promptId);
}

// 编辑自定义指令
function editPrompt(promptId) {
  const prompt = customPrompts.find(p => p.id === promptId);
  if (!prompt) return;

  editingPromptId = promptId;
  customPromptName.value = prompt.name;
  customPrompt.value = prompt.prompt;
  customPromptForm.style.display = 'block';

  // 滚动到表单
  customPromptForm.scrollIntoView({ behavior: 'smooth' });
}

// 删除自定义指令
function deletePrompt(promptId) {
  const confirmMessage = window.i18n ? window.i18n.t('settings.ai.deleteConfirm') : 'Are you sure you want to delete this assistant?';
  if (!confirm(confirmMessage)) return;

  customPrompts = customPrompts.filter(p => p.id !== promptId);
  saveCustomPrompts();
  renderCustomPrompts();

  // 如果删除的是当前选择的指令，重置选择
  if (selectedPromptId === promptId) {
    selectedPromptId = customPrompts.length > 0 ? customPrompts[0].id : '';
  }
}

// 保存自定义指令
function savePrompt() {
  const name = customPromptName.value.trim();
  const promptText = customPrompt.value.trim();

  if (!name) {
    const nameRequiredMessage = window.i18n ? window.i18n.t('settings.ai.nameRequired') : 'Please enter prompt name';
    alert(nameRequiredMessage);
    return;
  }

  if (!promptText) {
    const contentRequiredMessage = window.i18n ? window.i18n.t('settings.ai.contentRequired') : 'Please enter prompt content';
    alert(contentRequiredMessage);
    return;
  }

  if (editingPromptId) {
    // 编辑现有指令
    const index = customPrompts.findIndex(p => p.id === editingPromptId);
    if (index !== -1) {
      customPrompts[index].name = name;
      customPrompts[index].prompt = promptText;
    }
  } else {
    // 添加新指令
    const newPrompt = {
      id: generateId(),
      name: name,
      prompt: promptText
    };
    customPrompts.push(newPrompt);
  }

  saveCustomPrompts();
  renderCustomPrompts();
  resetCustomPromptForm();
}

// 重置自定义指令表单
function resetCustomPromptForm() {
  editingPromptId = null;
  customPromptName.value = '';
  customPrompt.value = '';
  customPromptForm.style.display = 'none';
}

// 生成唯一ID
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

// 从localStorage加载设置
function loadSettings() {
  try {
    const savedSettings = localStorage.getItem('meetingGptSettings');
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      console.log('加载已保存的设置:', settings);

      // 应用加载的设置
      if (settings.silenceThreshold !== undefined) {
        silenceThreshold = settings.silenceThreshold;
        silenceThresholdInput.value = silenceThreshold;
      }

      if (settings.silenceDuration !== undefined) {
        silenceDuration = settings.silenceDuration;
        silenceDurationInput.value = silenceDuration;
      }



      if (settings.selectedPromptId) {
        selectedPromptId = settings.selectedPromptId;
      }

      // 保存音频设备ID到变量，但不立即设置下拉框，会在loadAudioDevices中设置
      if (settings.selectedAudioDeviceId) {
        selectedAudioDeviceId = settings.selectedAudioDeviceId;
        console.log('已加载保存的音频设备ID:', selectedAudioDeviceId);
      }
    }
  } catch (error) {
    console.error('加载设置时出错:', error);
  }
}

// 保存设置到localStorage
function saveSettings(showAlert = false) {
  // 确保获取当前选择的音频设备ID
  selectedAudioDeviceId = audioDevicesSelect.value;

  const settings = {
    silenceThreshold: silenceThreshold,
    silenceDuration: silenceDuration,
    selectedPromptId: selectedPromptId,
    selectedAudioDeviceId: selectedAudioDeviceId
  };

  localStorage.setItem('meetingGptSettings', JSON.stringify(settings));
  console.log('设置已保存:', settings);
  console.log('已保存音频设备ID:', selectedAudioDeviceId);

  // 同时保存录音状态到主进程
  saveRecordingState();

  // 只有当显式要求显示弹窗时才显示
  if (showAlert) {
    const savedMessage = window.i18n ? window.i18n.t('settings.saved') : 'Settings saved successfully';
    alert(savedMessage);
  }
}

// 修改 saveRecordingState 函数
function saveRecordingState() {
  console.log('设置页面：保存录音相关设置到主进程 (不包括isRecording的主状态)。');
  ipcRenderer.send('save-recording-state', {
    // isRecording: false, // 不从此页面控制或发送 isRecording 主状态
    audioDeviceId: selectedAudioDeviceId,
    silenceThreshold: silenceThreshold,
    silenceDuration: silenceDuration
    // 如果设置页面有其他影响录音的参数，也应在此处发送
  });
}

// 事件监听器
silenceThresholdInput.addEventListener('change', () => {
  silenceThreshold = parseFloat(silenceThresholdInput.value);
  saveRecordingState();
});

silenceDurationInput.addEventListener('change', () => {
  silenceDuration = parseInt(silenceDurationInput.value);
  saveRecordingState();
});



addCustomPromptBtn.addEventListener('click', () => {
  resetCustomPromptForm();
  customPromptForm.style.display = 'block';
  customPromptName.focus();
});

cancelCustomPromptBtn.addEventListener('click', resetCustomPromptForm);

saveCustomPromptBtn.addEventListener('click', savePrompt);

saveSettingsBtn.addEventListener('click', () => {
  saveSettings(true); // 用户点击保存按钮时显示弹窗
});

audioDevicesSelect.addEventListener('change', () => {
  selectedAudioDeviceId = audioDevicesSelect.value;
  stopVisualizing();
  startVisualizing();
  saveRecordingState();
});

// 窗口大小调整时重新设置可视化
window.addEventListener('resize', setupVisualizer);

// 页面聚焦和失焦时管理可视化
window.addEventListener('focus', startVisualizing);
window.addEventListener('blur', stopVisualizing);

// 显示用户信息
function showUserProfile(user) {
  const userProfileContainer = document.getElementById('userProfileContainer');
  const loginButtonContainer = document.getElementById('loginButtonContainer');
  const userEmailElement = document.getElementById('userEmail');
  const userAvatarElement = document.getElementById('userAvatar');

  console.log('显示用户信息:', user);

  if (userProfileContainer && loginButtonContainer && userEmailElement && userAvatarElement) {
    // 判断user的数据结构并正确获取email
    let email = '';
    if (user) {
      if (user.email) {
        // 直接有email属性
        email = user.email;
      } else if (user.data && user.data.email) {
        // 嵌套在data属性中
        email = user.data.email;
      } else if (typeof user === 'string') {
        // 直接是字符串
        email = user;
      }
    }

    // 显示用户信息
    userEmailElement.textContent = email || '未知用户';

    // 设置头像（使用邮箱首字母）
    const initial = (email && email.length > 0) ? email[0].toUpperCase() : 'U';
    userAvatarElement.textContent = initial;

    // 显示用户信息区域，隐藏登录按钮
    userProfileContainer.style.display = 'flex';
    loginButtonContainer.style.display = 'none';
  }
}

// 隐藏用户信息，显示登录按钮
function hideUserProfile() {
  const userProfileContainer = document.getElementById('userProfileContainer');
  const loginButtonContainer = document.getElementById('loginButtonContainer');

  if (userProfileContainer && loginButtonContainer) {
    userProfileContainer.style.display = 'none';
    loginButtonContainer.style.display = 'flex';
  }
}

// 获取并显示用户订阅信息
async function loadUserSubscriptionInfo() {
  console.log('开始加载用户订阅信息...');

  try {
    // 首先检查用户是否已登录
    const isLoggedInFromStorage = localStorage.getItem('isLoggedIn') === 'true';
    console.log('localStorage中的登录状态:', isLoggedInFromStorage);

    if (!isLoggedInFromStorage) {
      console.log('用户未登录，显示默认订阅信息');
      showDefaultSubscriptionInfo();
      return;
    }

    // 直接从服务器获取最新的用户信息，不使用缓存
    console.log('正在从服务器获取最新用户信息...');
    const result = await ipcRenderer.invoke('get-user-info', { skipCache: true });
    console.log('获取用户信息结果:', JSON.stringify(result, null, 2));

    if (result && result.success && result.data) {
      const userInfo = result.data;
      console.log('用户信息数据:', JSON.stringify(userInfo, null, 2));

      // 保存到localStorage作为缓存
      try {
        localStorage.setItem('userInfo', JSON.stringify(userInfo));
        console.log('用户信息已缓存到localStorage');
      } catch (cacheError) {
        console.warn('缓存用户信息到localStorage失败:', cacheError);
      }

      // 检查订阅和权益信息
      if (userInfo.subscriptions && userInfo.subscriptions.length > 0) {
        console.log('找到subscriptions信息，调用showSubscriptionInfo');
        // 有订阅信息，显示完整的订阅信息（包括benefits）
        showSubscriptionInfo(userInfo.subscriptions[0], userInfo.benefits);
      } else if (userInfo.benefits && userInfo.benefits.length > 0) {
        console.log('只有benefits信息，调用showUserBenefitsInfo');
        // 只有benefits信息，没有订阅信息
        showUserBenefitsInfo(userInfo.benefits);
      } else {
        console.log('用户没有订阅信息或benefits信息，显示默认信息');
        showDefaultSubscriptionInfo();
      }
    } else {
      console.log('无法获取用户订阅信息，显示默认信息。result:', result);
      showDefaultSubscriptionInfo();
    }
  } catch (error) {
    console.error('加载用户订阅信息出错:', error);
    showDefaultSubscriptionInfo();
  }
}

// 格式化时间为本地时区
function formatToLocalTime(isoString) {
  if (!isoString) return '未知';

  try {
    const date = new Date(isoString);

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '时间格式错误';
    }

    // 使用本地时区格式化日期和时间
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });
  } catch (error) {
    console.error('格式化时间出错:', error);
    return '时间格式错误';
  }
}

// 格式化时间为用户本地时区和语言环境
function formatToLocalTime(isoString) {
  if (!isoString) return 'Unknown';

  try {
    const date = new Date(isoString);

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return 'Invalid date';
    }

    // 使用用户的本地语言环境和时区格式化日期和时间
    return date.toLocaleString(undefined, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });
  } catch (error) {
    console.error('格式化时间出错:', error);
    return 'Date format error';
  }
}

// 显示用户权益信息（仅有benefits，没有subscription信息）
function showUserBenefitsInfo(benefits) {
  const subscriptionInfoElement = document.querySelector('.subscription-info');
  if (!subscriptionInfoElement) {
    console.error('找不到subscription-info元素');
    return;
  }

  console.log('显示用户权益信息，benefits数据:', benefits);

  try {
    // 根据权益额度判断计划类型
    const audioTimeBenefit = benefits.find(b => b.benefit_type === 'audio_time');
    let planName = '免费计划';
    let badgeClass = 'free';

    if (audioTimeBenefit && audioTimeBenefit.amount > 0) {
      // 如果有音频时长权益，根据额度判断计划类型
      const totalMinutes = Math.round(audioTimeBenefit.amount / 60);
      if (totalMinutes > 10) { // 超过10分钟认为是付费计划
        planName = '付费计划';
        badgeClass = 'paid';
      }
    }

    // 计算音频时长权益信息
    let quotaInfo = { available: 0, total: 0, used: 0, percentage: 0 };

    if (audioTimeBenefit) {
      quotaInfo.total = Math.round(audioTimeBenefit.amount / 60 * 10) / 10;
      quotaInfo.used = Math.round(audioTimeBenefit.used_amount / 60 * 10) / 10;
      quotaInfo.available = Math.round(audioTimeBenefit.available / 60 * 10) / 10;
      quotaInfo.percentage = quotaInfo.total > 0 ? Math.round((quotaInfo.used / quotaInfo.total) * 100) : 0;
    }

    // 获取国际化文本
    const audioQuotaLabel = window.i18n ? window.i18n.t('settings.subscription.audioQuota') : 'Audio Transcription Quota';
    const minutesUnit = window.i18n ? window.i18n.t('settings.subscription.minutes') : 'minutes';
    const usedLabel = window.i18n ? window.i18n.t('settings.subscription.used') : 'Used';
    const upgradeText = window.i18n ? window.i18n.t('settings.subscription.upgrade') : 'Upgrade Plan';
    const manageText = window.i18n ? window.i18n.t('settings.subscription.manage') : 'Manage Subscription';
    const freePlanName = window.i18n ? window.i18n.t('subscription.plans.free') : '免费计划';

    const displayContent = `
      <div class="subscription-card">
        <div class="subscription-header">
          <span class="plan-badge ${badgeClass}">${planName}</span>
        </div>

        <div class="subscription-details">
          <div class="detail-row">
            <span class="detail-label">${audioQuotaLabel}</span>
            <span class="detail-value">${quotaInfo.available}/${quotaInfo.total} ${minutesUnit}</span>
          </div>

          <div class="quota-progress">
            <div class="quota-bar">
              <div class="quota-fill" style="width: ${quotaInfo.percentage}%"></div>
            </div>
            <div class="quota-text">
              <span>${usedLabel} ${quotaInfo.used} ${minutesUnit}</span>
              <span>${quotaInfo.percentage}%</span>
            </div>
          </div>
        </div>

        <div class="subscription-actions">
          <a href="subscription.html" class="upgrade-link">${planName === freePlanName ? upgradeText : manageText}</a>
        </div>
      </div>
    `;

    subscriptionInfoElement.innerHTML = displayContent;
    console.log('已更新订阅信息显示内容');
  } catch (error) {
    console.error('显示用户权益信息出错:', error);
    showDefaultSubscriptionInfo();
  }
}

// 显示订阅信息
function showSubscriptionInfo(subscription, benefits) {
  const subscriptionInfoElement = document.querySelector('.subscription-info');
  if (!subscriptionInfoElement) return;

  try {
    // 解析订阅信息
    const billingCycle = subscription.billing_cycle || '未知';

    // 处理下次计费时间，转换为本地时区
    const nextBilledAt = formatToLocalTime(subscription.next_billed_at);

    // 确定计划类型和徽章样式
    let planName = window.i18n ? window.i18n.t('subscription.plans.current') : '';
    let badgeClass = 'free';

    if (billingCycle && billingCycle !== '未知') {
      if (billingCycle.includes('month')) {
        planName = window.i18n ? window.i18n.t('subscription.plans.monthly') : 'Plan';
        badgeClass = 'monthly';
      } else if (billingCycle.includes('year')) {
        planName = window.i18n ? window.i18n.t('subscription.plans.yearly') : 'Plan';
        badgeClass = 'yearly';
      } else {
        planName = window.i18n ? window.i18n.t('subscription.plans.subscription') : '';
        badgeClass = 'paid';
      }
    } else {
      planName = window.i18n ? window.i18n.t('subscription.plans.free') : '';
      badgeClass = 'free';
    }

    // 计算音频时长权益信息
    const audioTimeBenefit = benefits?.find(b => b.benefit_type === 'audio_time');
    let quotaInfo = { available: 0, total: 0, used: 0, percentage: 0 };

    if (audioTimeBenefit) {
      quotaInfo.total = Math.round(audioTimeBenefit.amount / 60 * 10) / 10;
      quotaInfo.used = Math.round(audioTimeBenefit.used_amount / 60 * 10) / 10;
      quotaInfo.available = Math.round(audioTimeBenefit.available / 60 * 10) / 10;
      quotaInfo.percentage = quotaInfo.total > 0 ? Math.round((quotaInfo.used / quotaInfo.total) * 100) : 0;
    }

    // 获取国际化文本
    const audioQuotaLabel = window.i18n ? window.i18n.t('settings.subscription.audioQuota') : 'Audio Transcription Quota';
    const minutesUnit = window.i18n ? window.i18n.t('settings.subscription.minutes') : 'minutes';
    const usedLabel = window.i18n ? window.i18n.t('settings.subscription.used') : 'Used';
    const nextBillingLabel = window.i18n ? window.i18n.t('settings.subscription.nextBilling') : 'Next Billing';
    const manageText = window.i18n ? window.i18n.t('settings.subscription.manage') : 'Manage Subscription';

    // 构建新的卡片式显示内容
    const displayContent = `
      <div class="subscription-card">
        <div class="subscription-header">
          <span class="plan-badge ${badgeClass}">${planName}</span>
        </div>

        <div class="subscription-details">
          <div class="detail-row">
            <span class="detail-label">${audioQuotaLabel}</span>
            <span class="detail-value">${quotaInfo.available}/${quotaInfo.total} ${minutesUnit}</span>
          </div>

          <div class="quota-progress">
            <div class="quota-bar">
              <div class="quota-fill" style="width: ${quotaInfo.percentage}%"></div>
            </div>
            <div class="quota-text">
              <span>${usedLabel} ${quotaInfo.used} ${minutesUnit}</span>
              <span>${quotaInfo.percentage}%</span>
            </div>
          </div>

          ${nextBilledAt && nextBilledAt !== 'Unknown' && nextBilledAt !== 'Invalid date' && nextBilledAt !== 'Date format error' ? `
          <div class="detail-row">
            <span class="detail-label">${nextBillingLabel}</span>
            <span class="detail-value">${nextBilledAt}</span>
          </div>
          ` : ''}
        </div>

        <div class="subscription-actions">
          <a href="subscription.html" class="upgrade-link manage">${manageText}</a>
        </div>
      </div>
    `;

    subscriptionInfoElement.innerHTML = displayContent;
  } catch (error) {
    console.error('显示订阅信息出错:', error);
    showDefaultSubscriptionInfo();
  }
}

// 显示默认订阅信息（未登录或无订阅时）
function showDefaultSubscriptionInfo() {
  const subscriptionInfoElement = document.querySelector('.subscription-info');
  if (!subscriptionInfoElement) {
    console.error('找不到subscription-info元素');
    return;
  }

  console.log('显示默认订阅信息');

  // 检查是否已登录
  const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';

  if (isLoggedIn) {
    // 获取国际化文本
    const loadFailedText = window.i18n ? window.i18n.t('settings.subscription.loadFailed') : 'Load Failed';
    const audioQuotaLabel = window.i18n ? window.i18n.t('settings.subscription.audioQuota') : 'Audio Transcription Quota';
    const cannotGetText = window.i18n ? window.i18n.t('settings.subscription.cannotGet') : 'Cannot Get';
    const refreshText = window.i18n ? window.i18n.t('common.refresh') : 'Refresh';
    const refreshingText = window.i18n ? window.i18n.t('settings.subscription.refreshing') : 'Refreshing...';
    const upgradeText = window.i18n ? window.i18n.t('settings.subscription.upgrade') : 'Upgrade';

    // 已登录但获取信息失败，显示重试选项
    subscriptionInfoElement.innerHTML = `
      <div class="subscription-card">
        <div class="subscription-header">
          <span class="plan-badge free">${loadFailedText}</span>
        </div>

        <div class="subscription-details">
          <div class="detail-row">
            <span class="detail-label">${audioQuotaLabel}</span>
            <span class="detail-value">${cannotGetText}</span>
          </div>
        </div>

        <div class="subscription-actions">
          <button id="refreshQuotaBtn" style="margin-right: 10px; padding: 6px 12px; font-size: 12px; background: #1a73e8; color: white; border: none; border-radius: 4px; cursor: pointer;">${refreshText}</button>
          <a href="subscription.html" class="upgrade-link">${upgradeText}</a>
        </div>
      </div>
    `;

    // 添加刷新按钮事件监听
    const refreshBtn = document.getElementById('refreshQuotaBtn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', async () => {
        refreshBtn.textContent = refreshingText;
        refreshBtn.disabled = true;

        // 使用强制刷新函数
        await forceRefreshUserInfo();

        refreshBtn.textContent = refreshText;
        refreshBtn.disabled = false;
      });
    }
  } else {
    // 获取国际化文本
    const freeVersionText = window.i18n ? window.i18n.t('settings.subscription.freeVersion') : 'Free Version';
    const audioQuotaLabel = window.i18n ? window.i18n.t('settings.subscription.audioQuota') : 'Audio Transcription Quota';
    const pleaseLoginText = window.i18n ? window.i18n.t('settings.subscription.pleaseLogin') : 'Please login to view';
    const upgradeText = window.i18n ? window.i18n.t('settings.subscription.upgrade') : 'Upgrade Plan';

    // 未登录
    subscriptionInfoElement.innerHTML = `
      <div class="subscription-card">
        <div class="subscription-header">
          <span class="plan-badge free">${freeVersionText}</span>
        </div>

        <div class="subscription-details">
          <div class="detail-row">
            <span class="detail-label">${audioQuotaLabel}</span>
            <span class="detail-value">${pleaseLoginText}</span>
          </div>
        </div>

        <div class="subscription-actions">
          <a href="subscription.html" class="upgrade-link">${upgradeText}</a>
        </div>
      </div>
    `;
  }
}

// 强制刷新用户信息（清除所有缓存）
async function forceRefreshUserInfo() {
  console.log('强制刷新用户信息...');

  // 清除所有相关缓存
  localStorage.removeItem('userInfo');
  localStorage.removeItem('isLoggedIn');

  // 重新加载用户订阅信息
  await loadUserSubscriptionInfo();
}

// 将强制刷新函数暴露到全局，方便在控制台调用
window.forceRefreshUserInfo = forceRefreshUserInfo;

// 检查用户登录状态
async function checkLoginStatus() {
  try {
    // 首先尝试从localStorage获取用户信息
    try {
      const isLoggedInFromStorage = localStorage.getItem('isLoggedIn') === 'true';
      const userInfoFromStorage = localStorage.getItem('userInfo');

      if (isLoggedInFromStorage && userInfoFromStorage) {
        const userInfo = JSON.parse(userInfoFromStorage);
        console.log('从localStorage获取到用户信息:', userInfo.email);
        showUserProfile(userInfo);
        return true;
      }
    } catch (storageError) {
      console.error('从localStorage检查登录状态出错:', storageError);
    }

    // 如果localStorage中没有，则从服务器获取
    const result = await ipcRenderer.invoke('get-user-info', { skipCache: true });
    console.log('从服务器获取用户信息结果:', result);

    if (result && result.success && (result.isLoggedIn || result.data)) {
      // 根据返回结构确定用户信息
      const userInfo = result.data || result.userInfo;
      if (userInfo) {
        showUserProfile(userInfo);
        return true;
      }
    }

    // 如果没有登录信息，显示登录按钮
    hideUserProfile();
    return false;
  } catch (error) {
    console.error('检查登录状态出错:', error);
    hideUserProfile();
    return false;
  }
}

// 处理导航链接
document.addEventListener('DOMContentLoaded', async () => {
  // 等待国际化初始化完成
  if (window.i18n) {
    await window.i18n.init();
    // 初始化完成后立即更新页面内容
    window.i18n.updatePageContent();
  }

  setupVisualizer();

  // 修改加载顺序，确保先加载设置，然后加载设备，设备加载后会应用保存的设备选择
  loadSettings();
  await loadAudioDevices();

  loadCustomPrompts();
  startVisualizing();

  // 检查用户登录状态
  await checkLoginStatus();

  // 加载用户订阅信息
  await loadUserSubscriptionInfo();

  // 初始化语言设置
  initializeLanguageSettings();

  // 加载完成后，立即保存录音状态到主进程
  saveRecordingState();

  // 处理导航链接
  document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', (event) => {
      // 阻止默认行为
      event.preventDefault();

      // 停止可视化
      stopVisualizing();

      // 保存设置（不显示弹窗）
      saveSettings(false);

      // 使用IPC导航
      const href = link.getAttribute('href');
      if (href) {
        console.log('导航到:', href);
        ipcRenderer.send('navigate', href);
      }
    });
  });

  // 特殊处理"使用指南"链接
  const userGuideLink = document.getElementById('userGuideLink');
  const footerUserGuideLink = document.getElementById('footerUserGuideLink');

  // 定义处理函数
  const handleUserGuideLinkClick = async (event) => {
    // 阻止默认行为
    event.preventDefault();

    // 停止可视化
    stopVisualizing();

    // 保存设置（不显示弹窗）
    saveSettings(false);

    try {
      // 从环境变量获取网站URL
      const websiteUrlResult = await ipcRenderer.invoke('get-env-var', 'WEBSITE_URL');
      if (!websiteUrlResult.success || !websiteUrlResult.value) {
        throw new Error('无法获取网站URL，请检查环境配置');
      }

      // 使用shell模块打开浏览器
      const { shell } = require('electron');
      await shell.openExternal(websiteUrlResult.value);
    } catch (error) {
      console.error('跳转到使用指南页面失败:', error);
      alert('跳转到使用指南页面失败: ' + error.message);
    }
  };

  // 为导航栏中的使用指南链接添加点击事件
  if (userGuideLink) {
    userGuideLink.addEventListener('click', handleUserGuideLinkClick);
  }

  // 为页脚中的使用指南链接添加点击事件
  if (footerUserGuideLink) {
    footerUserGuideLink.addEventListener('click', handleUserGuideLinkClick);
  }
});

// 显示消息提示
function showMessage(message, type = 'info') {
  // 创建消息元素
  const messageEl = document.createElement('div');
  messageEl.className = `message message-${type}`;
  messageEl.textContent = message;

  // 添加样式
  messageEl.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 10000;
    font-size: 14px;
    max-width: 300px;
    word-wrap: break-word;
  `;

  // 添加到页面
  document.body.appendChild(messageEl);

  // 3秒后自动移除
  setTimeout(() => {
    if (messageEl.parentNode) {
      messageEl.parentNode.removeChild(messageEl);
    }
  }, 3000);
}

// 初始化语言设置
function initializeLanguageSettings() {
  const languageSelect = document.getElementById('languageSelect');
  const currentLanguageDisplay = document.getElementById('currentLanguageDisplay');

  if (!languageSelect || !window.i18n) return;

  // 设置当前语言
  const currentLang = window.i18n.getCurrentLanguage();
  languageSelect.value = currentLang;

  // 更新当前语言显示
  if (currentLanguageDisplay) {
    const supportedLanguages = window.i18n.getSupportedLanguages();
    currentLanguageDisplay.textContent = supportedLanguages[currentLang];
  }

  // 监听语言切换
  languageSelect.addEventListener('change', async (e) => {
    const newLanguage = e.target.value;

    if (window.i18n) {
      await window.i18n.switchLanguage(newLanguage);

      // 更新当前语言显示
      if (currentLanguageDisplay) {
        const supportedLanguages = window.i18n.getSupportedLanguages();
        currentLanguageDisplay.textContent = supportedLanguages[newLanguage];
      }

      // 显示保存成功消息
      if (window.i18n.t) {
        const message = window.i18n.t('settings.saved');
        showMessage(message, 'success');
      }
    }
  });

  // 监听语言切换事件
  window.addEventListener('languageChanged', (e) => {
    const newLanguage = e.detail.language;
    languageSelect.value = newLanguage;

    if (currentLanguageDisplay) {
      const supportedLanguages = window.i18n.getSupportedLanguages();
      currentLanguageDisplay.textContent = supportedLanguages[newLanguage];
    }

    // 重新渲染订阅信息以应用新语言
    loadUserSubscriptionInfo();

    // 重新渲染自定义指令列表
    renderCustomPrompts();

    // 强制更新所有动态内容
    updateDynamicContent();
  });
}

// 更新动态内容
function updateDynamicContent() {
  if (!window.i18n) return;

  // 更新音频设备下拉菜单中的错误信息
  const audioDevicesSelect = document.getElementById('audioDevices');
  if (audioDevicesSelect && audioDevicesSelect.options.length === 1) {
    const option = audioDevicesSelect.options[0];
    if (option.value === '') {
      const errorMessage = window.i18n.t('settings.audio.microphoneError');
      option.textContent = errorMessage;
    }
  }

  // 更新自定义指令选择器的占位符
  const selectedCustomPrompt = document.getElementById('selectedCustomPrompt');
  if (selectedCustomPrompt && selectedCustomPrompt.options.length === 0) {
    const noPromptsMessage = window.i18n.t('settings.ai.noCustomPrompts');
    selectedCustomPrompt.innerHTML = `<option value="">${noPromptsMessage}</option>`;
  }

  // 更新订阅信息加载文本
  const subscriptionInfo = document.querySelector('.subscription-info span');
  if (subscriptionInfo && subscriptionInfo.textContent.includes('Loading') || subscriptionInfo.textContent.includes('正在加载')) {
    subscriptionInfo.textContent = window.i18n.t('settings.subscription.loading');
  }
}

// 页面关闭时停止可视化
window.addEventListener('beforeunload', stopVisualizing);