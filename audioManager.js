const AudioManager = {
  audioContext: null,
  audioStream: null,
  analyser: null,
  scriptProcessor: null,
  isRecordingInternal: false,
  isSpeaking: false,
  silenceStartTime: null,
  audioChunks: [],
  silenceThreshold: 0.01,
  silenceDuration: 2000,
  silenceCounterThreshold: 3,
  selectedAudioDeviceId: '',
  chunkDurationMs: 0,
  silenceCounter: 0,

  onStatusUpdate: null,
  onRecordingStateChange: null,
  onDataReady: null,
  onVisualizerData: null,
  _getCurrentPrompt: () => 'Default prompt if not set',

  configure: function(settings) {
    if (settings.silenceThreshold !== undefined) this.silenceThreshold = settings.silenceThreshold;
    if (settings.silenceDuration !== undefined) this.silenceDuration = settings.silenceDuration;
    if (settings.silenceCounterThreshold !== undefined) this.silenceCounterThreshold = settings.silenceCounterThreshold;
    if (settings.selectedAudioDeviceId !== undefined) this.selectedAudioDeviceId = settings.selectedAudioDeviceId;

    if (settings.onStatusUpdate) this.onStatusUpdate = settings.onStatusUpdate;
    if (settings.onRecordingStateChange) this.onRecordingStateChange = settings.onRecordingStateChange;
    if (settings.onDataReady) this.onDataReady = settings.onDataReady;
    if (settings.onVisualizerData) this.onVisualizerData = settings.onVisualizerData;
    if (settings.getCurrentPrompt) this._getCurrentPrompt = settings.getCurrentPrompt;

    console.log('AudioManager configured:', {
      silenceThreshold: this.silenceThreshold,
      silenceDuration: this.silenceDuration,
      silenceCounterThreshold: this.silenceCounterThreshold,
      selectedAudioDeviceId: this.selectedAudioDeviceId
    });
  },

  isRecording: function() {
    return this.isRecordingInternal;
  },

  start: async function() {
    if (this.isRecordingInternal) {
      console.warn('AudioManager: Recording already in progress.');
      return false;
    }

    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

      const constraints = {
        audio: {
          deviceId: this.selectedAudioDeviceId ? { exact: this.selectedAudioDeviceId } : undefined,
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
          sampleRate: 44100,
          channelCount: 2
        }
      };
      this.audioStream = await navigator.mediaDevices.getUserMedia(constraints);

      const source = this.audioContext.createMediaStreamSource(this.audioStream);
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 256;
      source.connect(this.analyser);

      this.scriptProcessor = this.audioContext.createScriptProcessor(4096, 1, 1);
      if (this.audioContext.sampleRate > 0 && this.scriptProcessor.bufferSize > 0) {
          this.chunkDurationMs = (this.scriptProcessor.bufferSize / this.audioContext.sampleRate) * 1000;
          console.log(`AudioManager: Each audio chunk duration: ${this.chunkDurationMs.toFixed(2)} ms`);
      } else {
          this.chunkDurationMs = (4096 / 44100) * 1000;
          console.warn('AudioManager: Could not accurately calculate chunkDurationMs, using default.');
      }

      source.connect(this.scriptProcessor);
      this.scriptProcessor.connect(this.audioContext.destination);

      this.isRecordingInternal = true;
      this.isSpeaking = false;
      this.silenceStartTime = null;
      this.audioChunks = [];
      this.silenceCounter = 0;

      this.scriptProcessor.onaudioprocess = this._onAudioProcess.bind(this);

      if (this.onStatusUpdate) this.onStatusUpdate('Listening...', 'recording');
      if (this.onRecordingStateChange) this.onRecordingStateChange(true);
      console.log('AudioManager: Recording started with deviceId:', this.selectedAudioDeviceId);
      return true;

    } catch (err) {
      console.error('AudioManager: Error starting recording:', err);
      if (this.onStatusUpdate) this.onStatusUpdate('Error: ' + err.message, 'idle');
      this.isRecordingInternal = false;
      if (this.onRecordingStateChange) this.onRecordingStateChange(false);
      if (this.audioStream) this.audioStream.getTracks().forEach(track => track.stop());
      if (this.audioContext && this.audioContext.state !== 'closed') this.audioContext.close().catch(e => console.error("AudioManager: Error closing AudioContext on start failure:", e));
      this.audioStream = null;
      this.audioContext = null;
      this.scriptProcessor = null;
      this.analyser = null;
      return false;
    }
  },

  _onAudioProcess: function(e) {
    if (!this.isRecordingInternal) return;

    const inputData = e.inputBuffer.getChannelData(0);
    let sum = 0;
    for (let i = 0; i < inputData.length; i++) {
      sum += Math.abs(inputData[i]);
    }
    const average = sum / inputData.length;

    if (this.analyser && this.onVisualizerData) {
      const dataArray = new Uint8Array(this.analyser.frequencyBinCount);
      this.analyser.getByteFrequencyData(dataArray);
      this.onVisualizerData(dataArray);
    }

    if (average > this.silenceThreshold) {
      if (!this.isSpeaking) {
        this.isSpeaking = true;
        if (this.onStatusUpdate) this.onStatusUpdate('Speech detected', 'speaking');
      }
      this.audioChunks.push(new Float32Array(inputData));
      this.silenceCounter = 0;
      this.silenceStartTime = null;
    } else {
      if (this.isSpeaking) {
        this.audioChunks.push(new Float32Array(inputData));
        this.silenceCounter++;

        if (this.silenceCounter >= this.silenceCounterThreshold) {
          if (this.silenceStartTime === null) {
            this.silenceStartTime = Date.now();
             console.log('AudioManager: Main silence timer started at:', new Date(this.silenceStartTime));
          } else {
            if (Date.now() - this.silenceStartTime > this.silenceDuration) {
              console.log('AudioManager: Silence duration exceeded. Processing audio.');
              const chunksToProcess = [...this.audioChunks];
              const startTimeForTrimming = this.silenceStartTime;

              this.isSpeaking = false;
              if (this.onStatusUpdate) this.onStatusUpdate('Listening...', 'recording');
              this.audioChunks = [];
              this.silenceCounter = 0;
              this.silenceStartTime = null;

              this._processAndSendData(chunksToProcess, startTimeForTrimming);
            }
          }
        }
      }
    }
  },

  _processAndSendData: function(chunks, segmentSilenceStartTime) {
    if (!chunks || chunks.length === 0) {
      console.log('AudioManager: No audio data to process.');
      return;
    }
     if (this.onStatusUpdate) this.onStatusUpdate('Processing audio...', 'processing');

    let processedChunks = [...chunks];

    if (this.chunkDurationMs > 0 && segmentSilenceStartTime !== null) {
      const numChunksTargetToTrim = Math.floor(this.silenceDuration / this.chunkDurationMs);
      let actualChunksRemoved = 0;
      for (let k = 0; k < numChunksTargetToTrim; k++) {
        if (processedChunks.length <= 1 && k < numChunksTargetToTrim - 1) {
            const singleChunk = processedChunks[0];
            let singleSum = 0;
            for(let j=0; j<singleChunk.length; j++) singleSum += Math.abs(singleChunk[j]);
            if((singleSum/singleChunk.length) < this.silenceThreshold) { /* allow removal */ } else { break; }
        }
        if(processedChunks.length === 0) break;

        const lastChunkIndex = processedChunks.length - 1;
        const chunkToInspect = processedChunks[lastChunkIndex];
        let sum = 0;
        for (let j = 0; j < chunkToInspect.length; j++) sum += Math.abs(chunkToInspect[j]);
        const average = sum / chunkToInspect.length;

        if (average < this.silenceThreshold) {
            processedChunks.pop();
            actualChunksRemoved++;
        } else {
            console.log("AudioManager: Tail chunk is not silent, stopping trim.");
            break;
        }
      }
      if (actualChunksRemoved > 0) {
          console.log(`AudioManager: Trimmed ${actualChunksRemoved} tail silent chunks.`);
      }
    }

    if (processedChunks.length === 0) {
      console.log('AudioManager: No audio data left after trimming.');
      if (this.onStatusUpdate) this.onStatusUpdate('Listening...', 'recording');
      return;
    }

    const totalLength = processedChunks.reduce((acc, val) => acc + val.length, 0);
    const mergedAudio = new Float32Array(totalLength);
    let offset = 0;
    for (const chunk of processedChunks) {
      mergedAudio.set(chunk, offset);
      offset += chunk.length;
    }

    const prompt = this._getCurrentPrompt();
    console.log(`🎯 AudioManager: 获取到的prompt: ${prompt.substring(0,100)}...`);
    console.log(`🎯 AudioManager: Sending audio data. Prompt: ${prompt.substring(0,50)}...`);

    if (this.onDataReady) {
      this.onDataReady(Array.from(mergedAudio), prompt, 'wav', Date.now());
    } else {
        console.error("AudioManager: onDataReady callback is not configured!");
    }
  },

  stop: function() {
    if (!this.isRecordingInternal && !this.audioStream && !this.audioContext) {
      // 🔧 修复：将警告改为调试信息，减少不必要的控制台噪音
      console.log('AudioManager: Stop called but not recording or already stopped.');
      if (this.onStatusUpdate) this.onStatusUpdate('Stopped', 'idle');
      if (this.onRecordingStateChange) this.onRecordingStateChange(false);
      return;
    }

    console.log('AudioManager: Stopping recording...');
    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop());
      this.audioStream = null;
    }
    if (this.scriptProcessor) {
      this.scriptProcessor.disconnect();
      this.scriptProcessor.onaudioprocess = null;
      this.scriptProcessor = null;
    }
    if (this.analyser) {
        this.analyser.disconnect();
        this.analyser = null;
    }
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close().then(() => console.log("AudioManager: AudioContext closed.")).catch(e => console.error("AudioManager: Error closing AudioContext:", e));
      this.audioContext = null;
    }

    this.isRecordingInternal = false;
    this.isSpeaking = false;
    this.silenceStartTime = null;
    this.audioChunks = [];

    if (this.onStatusUpdate) this.onStatusUpdate('Stopped', 'idle');
    if (this.onRecordingStateChange) this.onRecordingStateChange(false);
  }
};

module.exports = AudioManager;