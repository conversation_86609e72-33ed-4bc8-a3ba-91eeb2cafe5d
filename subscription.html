<!DOCTYPE html>
<html data-i18n-title="subscription.title">

<head>
  <meta charset="UTF-8">
  <title>MeetingGPT - Subscription</title>
  <style>
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f7fa;
      color: #2c3e50;
      display: flex;
      flex-direction: column;
      min-height: 100vh;
    }

    .header {
      background-color: #1a73e8;
      color: white;
      padding: 0;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .navbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 10px 20px;
    }

    .logo {
      display: flex;
      align-items: center;
    }

    .logo h1 {
      margin: 0;
      font-size: 1.6em;
      font-weight: 500;
    }

    .navigation {
      display: flex;
      align-items: center;
    }

    .nav-link {
      color: white;
      text-decoration: none;
      margin: 0 15px;
      padding: 5px 0;
      position: relative;
      font-weight: 500;
    }

    .nav-link:hover::after {
      width: 100%;
    }

    .login-button {
      background-color: white;
      color: #1a73e8;
      padding: 8px 16px;
      border-radius: 20px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s;
      text-decoration: none;
    }

    .login-button:hover {
      background-color: #f0f7ff;
      transform: translateY(-2px);
    }

    .main-container {
      flex: 1;
      max-width: 800px;
      margin: 40px auto;
      padding: 0 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .content-box {
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      padding: 40px;
      width: 100%;
      text-align: center;
    }

    h2 {
      font-size: 2.2em;
      color: #2c3e50;
      margin-bottom: 20px;
    }

    p {
      font-size: 1.2em;
      color: #718096;
      margin-bottom: 30px;
    }

    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      border-left-color: #1a73e8;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .footer {
      text-align: center;
      padding: 30px 0;
      color: #718096;
      font-size: 0.9em;
    }

    .footer-link {
      color: #1a73e8;
      margin: 0 10px;
      text-decoration: none;
    }

    .footer-link:hover {
      text-decoration: underline;
    }

    .message-container {
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      padding: 10px 20px;
      border-radius: 4px;
      z-index: 9999;
    }

    .message-container.info {
      background-color: #e3f2fd;
      color: #1976d2;
      border: 1px solid #1976d2;
    }

    .message-container.error {
      background-color: #ffebee;
      color: #e53935;
      border: 1px solid #e53935;
    }
  </style>
</head>

<body>
  <div class="header">
    <div class="navbar">
      <div class="logo">
        <h1 data-i18n="app.name">MeetingGPT</h1>
      </div>
      <div class="navigation">
        <a href="index.html" class="nav-link" data-i18n="navigation.home">Home</a>
        <a href="settings.html" class="nav-link" data-i18n="navigation.settings">Settings</a>
        <a href="subscription.html" class="nav-link" data-i18n="navigation.subscription">Subscription</a>
        <a class="nav-link" id="userGuideLink" data-i18n="navigation.userGuide">User Guide</a>
        <a href="login.html" class="login-button" data-i18n="navigation.login">Login</a>
      </div>
    </div>
  </div>

  <div class="main-container">
    <div class="content-box">
      <h2 data-i18n="subscription.title">Subscription Service</h2>
      <p data-i18n="subscription.redirecting">Redirecting to subscription page, please wait...</p>
      <div class="spinner"></div>
    </div>
  </div>

  <div class="footer">
    <p>
      <a href="index.html" class="footer-link" data-i18n="footer.backToHome">Back to Home</a>
      <a href="#" class="footer-link" data-i18n="footer.privacy">Privacy Policy</a>
      <a href="#" class="footer-link" data-i18n="footer.terms">Terms of Service</a>
      <a class="footer-link" id="footerUserGuideLink" data-i18n="navigation.userGuide">User Guide</a>
      <a href="mailto:<EMAIL>" class="footer-link" data-i18n="footer.contact">Contact Us</a>
    </p>
    <p><span data-i18n="footer.copyright">© 2025 MeetingGPT. All rights reserved.</span> | <span data-i18n="footer.email">Contact Email</span>: <a href="mailto:<EMAIL>" style="color: #1a73e8;"><EMAIL></a></p>
  </div>

  <script src="i18n.js"></script>
  <script src="languageSwitcher.js"></script>
  <script>
    const { ipcRenderer } = require('electron');

    // 页面导航
    document.querySelectorAll('.nav-link').forEach(link => {
      link.addEventListener('click', (event) => {
        if (link.getAttribute('href') === 'index.html') {
          event.preventDefault();
          ipcRenderer.send('navigate', 'index.html');
        }
      });
    });

    // 特殊处理"使用指南"链接
    const userGuideLink = document.getElementById('userGuideLink');
    const footerUserGuideLink = document.getElementById('footerUserGuideLink');

    // 定义处理函数
    const handleUserGuideLinkClick = async (event) => {
      // 阻止默认行为
      event.preventDefault();

      try {
        // 从环境变量获取网站URL
        const websiteUrlResult = await ipcRenderer.invoke('get-env-var', 'WEBSITE_URL');
        if (!websiteUrlResult.success || !websiteUrlResult.value) {
          throw new Error('无法获取网站URL，请检查环境配置');
        }

        // 使用shell模块打开浏览器
        const { shell } = require('electron');
        await shell.openExternal(websiteUrlResult.value);
      } catch (error) {
        console.error('跳转到使用指南页面失败:', error);
        showMessage('跳转到使用指南页面失败: ' + error.message, 'error');
      }
    };

    // 为导航栏中的使用指南链接添加点击事件
    if (userGuideLink) {
      userGuideLink.addEventListener('click', handleUserGuideLinkClick);
    }

    // 为页脚中的使用指南链接添加点击事件
    if (footerUserGuideLink) {
      footerUserGuideLink.addEventListener('click', handleUserGuideLinkClick);
    }

    // 显示消息提示
    function showMessage(message, type = 'error') {
      // 移除已有的消息
      const existingMessage = document.querySelector('.message-container');
      if (existingMessage) {
        existingMessage.remove();
      }

      // 创建新消息元素
      const messageContainer = document.createElement('div');
      messageContainer.className = `message-container ${type}`;
      messageContainer.textContent = message;

      document.body.appendChild(messageContainer);

      // 3秒后自动移除
      setTimeout(() => {
        messageContainer.style.opacity = '0';
        messageContainer.style.transition = 'opacity 0.5s';
        setTimeout(() => {
          if (messageContainer.parentNode) {
            messageContainer.parentNode.removeChild(messageContainer);
          }
        }, 500);
      }, 3000);
    }

    // 跳转到订阅网站
    async function redirectToSubscriptionWebsite() {
      try {
        // 从环境变量获取网站URL
        const websiteUrlResult = await ipcRenderer.invoke('get-env-var', 'WEBSITE_URL');
        if (!websiteUrlResult.success || !websiteUrlResult.value) {
          throw new Error('无法获取网站URL，请检查环境配置');
        }

        // 构建跳转URL
        const baseUrl = `${websiteUrlResult.value}/meeting/pricing`;

        // 使用shell模块打开浏览器
        const { shell } = require('electron');
        await shell.openExternal(baseUrl);

        // 导航回主页
        setTimeout(() => {
          ipcRenderer.send('navigate', 'index.html');
        }, 500);

        return true;
      } catch (error) {
        console.error('跳转到网站订阅页面失败:', error);
        showMessage('跳转到网站订阅页面失败: ' + error.message, 'error');

        // 显示失败信息
        document.querySelector('.content-box p').textContent = '跳转失败，请稍后重试';
        document.querySelector('.spinner').style.display = 'none';

        // 添加重试按钮
        const retryButton = document.createElement('button');
        retryButton.textContent = '重试';
        retryButton.style.backgroundColor = '#1a73e8';
        retryButton.style.color = 'white';
        retryButton.style.border = 'none';
        retryButton.style.padding = '12px 25px';
        retryButton.style.borderRadius = '4px';
        retryButton.style.cursor = 'pointer';
        retryButton.style.fontSize = '16px';
        retryButton.style.marginTop = '20px';
        retryButton.addEventListener('click', redirectToSubscriptionWebsite);

        document.querySelector('.content-box').appendChild(retryButton);

        return false;
      }
    }

    // 页面加载完成后自动跳转
    document.addEventListener('DOMContentLoaded', () => {
      // 延迟一小段时间再跳转，让用户看到加载中的页面
      setTimeout(() => {
        redirectToSubscriptionWebsite();
      }, 1000);
    });
  </script>
</body>

</html>