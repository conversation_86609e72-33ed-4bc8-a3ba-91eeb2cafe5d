const FormData = require('form-data');
const fs = require('fs');

/**
 * 后端通信模块
 */
class ApiClient {
  constructor() {
    this.baseUrl = ''; // 将在main.js中通过环境变量设置
  }

  /**
   * 设置API基础URL
   * @param {string} url 新的API基础URL
   */
  setBaseUrl(url) {
    if (!url || typeof url !== 'string') {
      console.error('无效的API基础URL:', url);
      return false;
    }

    // 移除末尾的斜杠
    this.baseUrl = url.replace(/\/+$/, '');
    console.log(`API基础URL已更新为: ${this.baseUrl}`);
    return true;
  }

  /**
   * 创建WAV格式文件
   * @param {Float32Array|Array} audioData 音频数据数组
   * @param {Object} options 配置选项
   * @param {number} options.sampleRate 采样率，默认为44100
   * @param {number} options.numChannels 通道数，默认为1（单声道）
   * @returns {Buffer} 包含WAV格式的Buffer
   */
  createWavFile(audioData, options = {}) {
    const sampleRate = options.sampleRate || 44100;
    const numChannels = options.numChannels || 1;
    const bitsPerSample = 16; // 使用16位采样
    const bytesPerSample = bitsPerSample / 8;
    const blockAlign = numChannels * bytesPerSample;
    const byteRate = sampleRate * blockAlign;

    // 确保audioData是Float32Array类型
    let samples;
    if (Array.isArray(audioData)) {
      samples = new Float32Array(audioData);
    } else if (audioData instanceof Float32Array) {
      samples = audioData;
    } else {
      throw new Error('音频数据必须是数组或Float32Array类型');
    }

    // 创建WAV文件头部分
    const dataSize = samples.length * bytesPerSample;
    const bufferSize = 44 + dataSize; // 44字节的WAV文件头 + 音频数据
    const buffer = Buffer.alloc(bufferSize);

    // RIFF标识符
    buffer.write('RIFF', 0);
    // 文件大小（不包括RIFF标识符和大小）
    buffer.writeUInt32LE(bufferSize - 8, 4);
    // WAVE标识符
    buffer.write('WAVE', 8);
    // fmt 子块标识符
    buffer.write('fmt ', 12);
    // fmt 子块大小（16字节）
    buffer.writeUInt32LE(16, 16);
    // 音频格式（1表示PCM）
    buffer.writeUInt16LE(1, 20);
    // 通道数
    buffer.writeUInt16LE(numChannels, 22);
    // 采样率
    buffer.writeUInt32LE(sampleRate, 24);
    // 每秒数据字节数
    buffer.writeUInt32LE(byteRate, 28);
    // 数据块对齐
    buffer.writeUInt16LE(blockAlign, 32);
    // 每个采样点的位数
    buffer.writeUInt16LE(bitsPerSample, 34);
    // data 子块标识符
    buffer.write('data', 36);
    // data 子块大小
    buffer.writeUInt32LE(dataSize, 40);

    // 写入音频数据
    let offset = 44; // 数据起始位置
    for (let i = 0; i < samples.length; i++) {
      // 将Float32Array中的值（-1.0到1.0）转换为16位PCM（-32768到32767）
      const sample = Math.max(-1, Math.min(1, samples[i]));
      const value = Math.floor(sample < 0 ? sample * 32768 : sample * 32767);
      buffer.writeInt16LE(value, offset);
      offset += bytesPerSample;
    }

    return buffer;
  }

  /**
   * 检查数据是否包含WAV文件头
   * @param {Uint8Array} data 要检查的数据
   * @returns {boolean} 是否为WAV格式
   */
  isWavFormat(data) {
    // 检查文件大小是否足够包含WAV头
    if (data.length < 44) return false;

    // 检查RIFF标识
    if (data[0] !== 0x52 || data[1] !== 0x49 || data[2] !== 0x46 || data[3] !== 0x46) return false;

    // 检查WAVE标识
    if (data[8] !== 0x57 || data[9] !== 0x41 || data[10] !== 0x56 || data[11] !== 0x45) return false;

    // 检查fmt 标识
    if (data[12] !== 0x66 || data[13] !== 0x6D || data[14] !== 0x74 || data[15] !== 0x20) return false;

    return true;
  }

  /**
   * 转录音频文件为文本
   * @param {string|Buffer|Blob|Float32Array|Array|Object} audioFile 音频文件路径、Buffer、Blob对象或音频数据数组
   * @param {string} model 模型名称，默认为whisper-1
   * @param {string} responseFormat 响应格式，默认为text
   * @param {Object} options 音频选项
   * @param {number} options.sampleRate 采样率，默认为44100
   * @param {number} options.numChannels 通道数，默认为1
   * @param {string} token 用户认证令牌
   * @returns {Promise<string>} 转录结果
   */
  async transcribeAudio(audioFile, model = 'whisper-1', responseFormat = 'verbose_json', options = {}, token = null) {
    try {
      const formData = new FormData();

      console.log('处理音频数据，类型:', typeof audioFile);

      // 处理音频文件
      let audioBuffer = null;
      const audioOptions = {
        sampleRate: options.sampleRate || 44100,
        numChannels: options.numChannels || 1
      };

      // 处理不同类型的音频输入
      if (typeof audioFile === 'string') {
        // 文件路径
        formData.append('file', fs.createReadStream(audioFile), {
          filename: 'audio.wav',
          contentType: 'audio/wav'
        });
      } else if (Buffer.isBuffer(audioFile)) {
        // Buffer数据
        audioBuffer = audioFile;
      } else if (audioFile instanceof Float32Array || Array.isArray(audioFile)) {
        // 原始音频数据，转换为WAV
        audioBuffer = this.createWavFile(audioFile, audioOptions);
      } else if (audioFile && typeof audioFile === 'object' && audioFile.audioData) {
        // 包含audioData字段的对象
        const audioData = audioFile.audioData;
        if (audioData instanceof Float32Array || Array.isArray(audioData)) {
          audioBuffer = this.createWavFile(audioData, audioOptions);
        } else if (Buffer.isBuffer(audioData)) {
          audioBuffer = audioData;
        } else {
          throw new Error('不支持的audioData格式');
        }
      } else {
        throw new Error('不支持的音频文件格式');
      }

      // 如果获取到了音频Buffer，添加到表单
      if (audioBuffer) {
        formData.append('file', audioBuffer, {
          filename: 'audio.wav',
          contentType: 'audio/wav'
        });
      }

      formData.append('model', model);
      formData.append('response_format', responseFormat);

      console.log('发送转录请求到:', `${this.baseUrl}/api/v1/transcriptions`);

      // 获取FormData边界和请求头
      const formHeaders = formData.getHeaders ? formData.getHeaders() : {};

      // 添加认证头
      if (token) {
        formHeaders['Authorization'] = `Bearer ${token}`;
      } else {
        console.warn('警告：未提供认证令牌，可能导致401错误');
      }

      // 使用FormData的管道方式发送请求
      return new Promise((resolve, reject) => {
        // 根据URL选择http或https模块
        const isHttps = this.baseUrl.startsWith('https:');
        const httpModule = isHttps ? require('https') : require('http');

        const request = httpModule.request(this.baseUrl + '/api/v1/transcriptions', {
          method: 'POST',
          headers: formHeaders,
          rejectUnauthorized: false // 开发环境可能需要
        }, (response) => {
          let data = '';

          response.on('data', (chunk) => {
            data += chunk;
          });

          response.on('end', () => {
            if (response.statusCode >= 200 && response.statusCode < 300) {
              console.log('转录请求成功');
              resolve(data);
            } else {
              console.error('转录API错误:', response.statusCode, data);
              reject(new Error(`音频转录失败: ${response.statusCode} - ${data}`));
            }
          });
        });

        request.on('error', (error) => {
          console.error('转录请求错误:', error.message);
          if (error.code === 'ECONNREFUSED') {
            console.error(`无法连接到服务器: ${this.baseUrl}`);
          }
          reject(error);
        });

        // 将FormData写入请求
        formData.pipe(request);
      });
    } catch (error) {
      console.error('音频转录错误:', error);
      throw error;
    }
  }

  /**
   * 获取聊天完成结果
   * @param {string} model 模型名称，默认为gpt-3.5-turbo
   * @param {Array} messages 消息数组，格式为[{role: "user", content: "文本内容"}]
   * @param {boolean} stream 是否使用流式输出，默认为true
   * @param {Function} onChunk 当stream为true时，用于处理每个数据块的回调函数
   * @param {string} token 用户认证令牌
   * @returns {Promise<Object|void>} 非流式输出时返回完整响应，流式输出时通过回调函数处理
   */
  async chatCompletion(model = 'gpt-3.5-turbo', messages = [], stream = true, onChunk = null, token = null) {
    try {
      // 如果启用了流式输出但没有提供回调函数，则抛出错误
      if (stream && (!onChunk || typeof onChunk !== 'function')) {
        throw new Error('使用流式输出时必须提供onChunk回调函数');
      }

      const requestBody = {
        model,
        messages,
        stream
      };

      console.log('发送聊天请求到:', `${this.baseUrl}/api/v1/chat/completions`);

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json'
      };

      // 添加认证头
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      } else {
        console.warn('警告：聊天API未提供认证令牌');
      }

      const response = await fetch(`${this.baseUrl}/api/v1/chat/completions`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('聊天API错误:', response.status, errorText);
        throw new Error(`聊天完成失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      if (stream) {
        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        const processStream = async () => {
          try {
            const { done, value } = await reader.read();

            if (done) {
              // 处理缓冲区中剩余的数据
              if (buffer.trim()) {
                try {
                  const chunks = buffer
                    .split('data: ')
                    .filter(line => line.trim() && line.trim() !== '[DONE]')
                    .map(line => {
                      try {
                        return JSON.parse(line.trim());
                      } catch (e) {
                        return null;
                      }
                    })
                    .filter(chunk => chunk !== null);

                  for (const chunk of chunks) {
                    onChunk(chunk);
                  }
                } catch (e) {
                  console.error('解析最终数据块失败:', e);
                }
              }

              onChunk({ done: true });
              return;
            }

            buffer += decoder.decode(value, { stream: true });

            // 处理完整的数据行
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              const trimmedLine = line.trim();
              if (trimmedLine.startsWith('data: ')) {
                const data = trimmedLine.substring(6).trim();

                if (data === '[DONE]') {
                  onChunk({ done: true });
                  continue;
                }

                try {
                  const chunk = JSON.parse(data);
                  onChunk(chunk);
                } catch (e) {
                  console.error('解析数据块失败:', e);
                }
              }
            }

            // 继续处理流
            processStream();
          } catch (error) {
            console.error('处理流式响应错误:', error);
            onChunk({ error: error.message, done: true });
          }
        };

        processStream();
      } else {
        // 非流式响应，返回完整的JSON结果
        const result = await response.json();
        console.log('聊天请求成功');
        return result;
      }
    } catch (error) {
      console.error('聊天完成错误:', error.message);
      if (error.cause && error.cause.code === 'ECONNREFUSED') {
        console.error(`无法连接到服务器: ${this.baseUrl}`);
      }
      throw error;
    }
  }

  /**
   * 用户登录
   * @param {string} email 用户邮箱
   * @param {string} password 用户密码
   * @returns {Promise<Object>} 登录结果，包含token和用户信息
   */
  async login(email, password) {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `登录失败: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('登录错误:', error);
      throw error;
    }
  }

  /**
   * 获取用户信息
   * @param {string} token 用户的授权令牌
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo(token) {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/user/info`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`获取用户信息失败: (${response.status}) ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API - getUserInfo 错误:', error);
      throw error;
    }
  }


}

module.exports = new ApiClient();