<!DOCTYPE html>
<html data-i18n-title="history.title">
<head>
  <meta charset="UTF-8">
  <title data-i18n="history.pageTitle">Chat History - MeetingGPT</title>
  <style>
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f7fa;
      color: #2c3e50;
    }

    .header {
      background-color: #1a73e8;
      color: white;
      padding: 0;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .navbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      padding: 10px 20px;
    }

    .logo {
      display: flex;
      align-items: center;
    }

    .logo h1 {
      margin: 0;
      font-size: 1.6em;
      font-weight: 500;
    }

    .navigation {
      display: flex;
      align-items: center;
    }

    .nav-link {
      color: white;
      text-decoration: none;
      margin: 0 15px;
      padding: 5px 0;
      position: relative;
      font-weight: 500;
    }

    .nav-link::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0;
      height: 2px;
      background-color: white;
      transition: width 0.3s;
    }

    .nav-link:hover::after {
      width: 100%;
    }

    .main-container {
      max-width: 1200px;
      margin: 20px auto;
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .page-header {
      background-color: #f8f9fa;
      padding: 20px;
      border-bottom: 1px solid #e1e4e8;
    }

    .page-title {
      margin: 0;
      color: #1a73e8;
      font-size: 1.8em;
      display: flex;
      align-items: center;
    }

    .page-title .icon {
      margin-right: 10px;
      font-size: 1.2em;
    }

    .sessions-container {
      padding: 20px;
    }

    .session-item {
      display: flex;
      align-items: center;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 10px;
      cursor: pointer;
      transition: all 0.3s;
      border: 1px solid #e1e4e8;
    }

    .session-item:hover {
      background-color: #f8f9fa;
      border-color: #1a73e8;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(26, 115, 232, 0.15);
    }

    .session-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background-color: #1a73e8;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      margin-right: 15px;
      flex-shrink: 0;
    }

    .session-content {
      flex: 1;
      min-width: 0;
    }

    .session-title {
      font-weight: 600;
      font-size: 16px;
      color: #2c3e50;
      margin-bottom: 5px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .session-preview {
      color: #6c757d;
      font-size: 14px;
      line-height: 1.4;
      margin-bottom: 5px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .session-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #8e9aaf;
    }

    .session-time {
      white-space: nowrap;
    }

    .session-count {
      background-color: #e8f5e9;
      color: #43a047;
      padding: 2px 8px;
      border-radius: 12px;
      font-weight: 500;
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #6c757d;
    }

    .empty-state .icon {
      font-size: 64px;
      margin-bottom: 20px;
      opacity: 0.5;
    }

    .empty-state h3 {
      margin: 0 0 10px 0;
      font-size: 18px;
    }

    .empty-state p {
      margin: 0;
      font-size: 14px;
    }

    .loading-state {
      text-align: center;
      padding: 40px 20px;
      color: #6c757d;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #1a73e8;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .back-button {
      background-color: #6c757d;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      margin-left: 20px;
      transition: all 0.3s;
    }

    .back-button:hover {
      background-color: #5a6268;
      transform: translateY(-1px);
    }

    .session-actions {
      display: flex;
      align-items: center;
      margin-left: 10px;
    }

    .delete-btn {
      background: none;
      border: none;
      color: #dc3545;
      cursor: pointer;
      padding: 5px;
      border-radius: 4px;
      font-size: 16px;
      opacity: 0;
      transition: all 0.3s;
    }

    .session-item:hover .delete-btn {
      opacity: 1;
    }

    .delete-btn:hover {
      background-color: #f8d7da;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="navbar">
      <div class="logo">
        <h1 data-i18n="app.name">MeetingGPT</h1>
      </div>
      <div class="navigation">
        <a href="index.html" class="nav-link" data-i18n="navigation.home">Home</a>
        <a href="settings.html" class="nav-link" data-i18n="navigation.settings">Settings</a>
        <a href="subscription.html" class="nav-link" data-i18n="navigation.subscription">Subscription</a>
        <button class="back-button" onclick="goBack()" data-i18n="common.back">Back</button>
      </div>
    </div>
  </div>

  <div class="main-container">
    <div class="page-header">
      <h2 class="page-title">
        <span class="icon">📝</span>
        <span data-i18n="history.title">Chat History</span>
      </h2>
    </div>

    <div class="sessions-container">
      <!-- 加载状态 -->
      <div id="loadingState" class="loading-state">
        <div class="loading-spinner"></div>
        <p data-i18n="history.loading">Loading chat sessions...</p>
      </div>

      <!-- 空状态 -->
      <div id="emptyState" class="empty-state" style="display: none;">
        <div class="icon">💬</div>
        <h3 data-i18n="history.empty.title">No Chat History</h3>
        <p data-i18n="history.empty.description">Start a conversation to see your chat history here.</p>
      </div>

      <!-- 会话列表 -->
      <div id="sessionsList" style="display: none;">
        <!-- 会话项将通过JavaScript动态生成 -->
      </div>
    </div>
  </div>

  <script src="i18n.js"></script>
  <script src="languageSwitcher.js"></script>
  <script src="history.js"></script>
</body>
</html>
