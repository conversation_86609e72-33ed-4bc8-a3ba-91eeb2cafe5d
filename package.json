{"name": "meetinggpt", "version": "1.0.0", "description": "Real-time voice detection and processing application", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac"}, "keywords": ["electron", "voice", "detection", "ai", "i18n", "multilingual"], "author": "", "license": "ISC", "dependencies": {"dotenv": "^16.5.0", "electron": "^30.0.0", "electron-builder": "^24.13.3", "electron-store": "^10.0.1", "highlight.js": "^11.11.1", "marked": "^15.0.11", "socket.io-client": "^4.7.5", "ws": "^8.16.0"}, "build": {"appId": "com.meetinggpt.app", "productName": "MeetingGPT", "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}}}