/**
 * 语言切换组件
 * 提供语言切换的UI组件和功能
 */

class LanguageSwitcher {
  constructor() {
    this.isInitialized = false;
    this.init();
  }

  /**
   * 初始化语言切换器
   */
  async init() {
    if (this.isInitialized) return;

    // 等待i18n初始化完成
    if (typeof window !== 'undefined' && window.i18n) {
      await this.waitForI18n();
      this.createSwitcher();
      this.bindEvents();
      this.isInitialized = true;
    }
  }

  /**
   * 等待i18n初始化完成
   */
  async waitForI18n() {
    return new Promise(async (resolve) => {
      const checkI18n = async () => {
        if (window.i18n) {
          // 确保i18n已初始化
          if (!window.i18n.initialized) {
            await window.i18n.init();
          }

          if (window.i18n.translations && Object.keys(window.i18n.translations).length > 0) {
            resolve();
          } else {
            setTimeout(checkI18n, 100);
          }
        } else {
          setTimeout(checkI18n, 100);
        }
      };
      checkI18n();
    });
  }

  /**
   * 创建语言切换器UI
   */
  createSwitcher() {
    // 检查是否已存在语言切换器
    if (document.getElementById('languageSwitcher')) {
      return;
    }

    const switcher = document.createElement('div');
    switcher.id = 'languageSwitcher';
    switcher.className = 'language-switcher';

    const currentLang = window.i18n.getCurrentLanguage();
    const supportedLanguages = window.i18n.getSupportedLanguages();

    switcher.innerHTML = `
      <div class="language-selector">
        <button class="language-button" id="languageButton">
          <span class="language-icon">🌐</span>
          <span class="language-text">${supportedLanguages[currentLang]}</span>
          <span class="dropdown-arrow">▼</span>
        </button>
        <div class="language-dropdown" id="languageDropdown">
          ${Object.entries(supportedLanguages).map(([code, name]) => `
            <div class="language-option ${code === currentLang ? 'active' : ''}" data-lang="${code}">
              <span class="language-flag">${this.getLanguageFlag(code)}</span>
              <span class="language-name">${name}</span>
              ${code === currentLang ? '<span class="check-mark">✓</span>' : ''}
            </div>
          `).join('')}
        </div>
      </div>
    `;

    // 添加样式
    this.addStyles();

    // 将切换器添加到导航栏
    const navbar = document.querySelector('.navigation');
    if (navbar) {
      navbar.appendChild(switcher);
    }
  }

  /**
   * 获取语言对应的旗帜图标
   */
  getLanguageFlag(langCode) {
    const flags = {
      'zh-CN': '🇨🇳',
      'en-US': '🇺🇸'
    };
    return flags[langCode] || '🌐';
  }

  /**
   * 添加样式
   */
  addStyles() {
    if (document.getElementById('languageSwitcherStyles')) {
      return;
    }

    const style = document.createElement('style');
    style.id = 'languageSwitcherStyles';
    style.textContent = `
      .language-switcher {
        position: relative;
        margin-left: 15px;
      }

      .language-selector {
        position: relative;
      }

      .language-button {
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.1);
        border: none;
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s ease;
        min-width: 100px;
        justify-content: space-between;
      }

      .language-button:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
      }

      .language-icon {
        margin-right: 6px;
        font-size: 16px;
      }

      .language-text {
        flex: 1;
        text-align: left;
      }

      .dropdown-arrow {
        margin-left: 6px;
        font-size: 10px;
        transition: transform 0.3s ease;
      }

      .language-button.active .dropdown-arrow {
        transform: rotate(180deg);
      }

      .language-dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        min-width: 150px;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s ease;
        margin-top: 8px;
        overflow: hidden;
      }

      .language-dropdown.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }

      .language-option {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        color: #333;
        cursor: pointer;
        transition: background-color 0.2s ease;
        font-size: 14px;
      }

      .language-option:hover {
        background-color: #f5f7fa;
      }

      .language-option.active {
        background-color: #e3f2fd;
        color: #1976d2;
        font-weight: 500;
      }

      .language-flag {
        margin-right: 8px;
        font-size: 16px;
      }

      .language-name {
        flex: 1;
      }

      .check-mark {
        color: #4caf50;
        font-weight: bold;
        margin-left: 8px;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .language-switcher {
          margin-left: 10px;
        }

        .language-button {
          padding: 6px 10px;
          min-width: 80px;
          font-size: 13px;
        }

        .language-dropdown {
          min-width: 120px;
        }

        .language-option {
          padding: 10px 12px;
          font-size: 13px;
        }
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    const languageButton = document.getElementById('languageButton');
    const languageDropdown = document.getElementById('languageDropdown');

    if (!languageButton || !languageDropdown) return;

    // 点击按钮切换下拉菜单
    languageButton.addEventListener('click', (e) => {
      e.stopPropagation();
      this.toggleDropdown();
    });

    // 点击语言选项
    languageDropdown.addEventListener('click', (e) => {
      const option = e.target.closest('.language-option');
      if (option) {
        const langCode = option.getAttribute('data-lang');
        this.switchLanguage(langCode);
      }
    });

    // 点击其他地方关闭下拉菜单
    document.addEventListener('click', () => {
      this.closeDropdown();
    });

    // 监听语言切换事件
    window.addEventListener('languageChanged', (e) => {
      this.updateSwitcher(e.detail.language);
    });
  }

  /**
   * 切换下拉菜单显示状态
   */
  toggleDropdown() {
    const dropdown = document.getElementById('languageDropdown');
    const button = document.getElementById('languageButton');

    if (dropdown && button) {
      const isShow = dropdown.classList.contains('show');
      if (isShow) {
        this.closeDropdown();
      } else {
        this.openDropdown();
      }
    }
  }

  /**
   * 打开下拉菜单
   */
  openDropdown() {
    const dropdown = document.getElementById('languageDropdown');
    const button = document.getElementById('languageButton');

    if (dropdown && button) {
      dropdown.classList.add('show');
      button.classList.add('active');
    }
  }

  /**
   * 关闭下拉菜单
   */
  closeDropdown() {
    const dropdown = document.getElementById('languageDropdown');
    const button = document.getElementById('languageButton');

    if (dropdown && button) {
      dropdown.classList.remove('show');
      button.classList.remove('active');
    }
  }

  /**
   * 切换语言
   */
  async switchLanguage(langCode) {
    if (window.i18n) {
      await window.i18n.switchLanguage(langCode);
      this.closeDropdown();
    }
  }

  /**
   * 更新切换器显示
   */
  updateSwitcher(currentLang) {
    const supportedLanguages = window.i18n.getSupportedLanguages();
    const languageText = document.querySelector('.language-text');
    const options = document.querySelectorAll('.language-option');

    // 更新按钮文本
    if (languageText) {
      languageText.textContent = supportedLanguages[currentLang];
    }

    // 更新选项状态
    options.forEach(option => {
      const langCode = option.getAttribute('data-lang');
      const checkMark = option.querySelector('.check-mark');

      if (langCode === currentLang) {
        option.classList.add('active');
        if (!checkMark) {
          option.innerHTML += '<span class="check-mark">✓</span>';
        }
      } else {
        option.classList.remove('active');
        if (checkMark) {
          checkMark.remove();
        }
      }
    });
  }
}

// 创建全局实例
const languageSwitcher = new LanguageSwitcher();

// 导出实例
if (typeof module !== 'undefined' && module.exports) {
  module.exports = languageSwitcher;
} else if (typeof window !== 'undefined') {
  window.languageSwitcher = languageSwitcher;
}
